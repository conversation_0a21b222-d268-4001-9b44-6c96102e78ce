# Events Feature Implementation Guide

## Overview

This guide provides a complete walkthrough of how the Events feature is implemented in the Dakoii Provincial Administration Manager plugin. Use this as a blueprint for implementing similar CRUD features.

## Architecture Overview

The Events feature follows a clean MVC-like pattern:
- **Model**: WordPress custom post type (`esp_event`) with custom meta fields
- **View**: Custom admin interface (`admin/views/events.php`)
- **Controller**: CRUD operations handled within the view file
- **Integration**: Menu registration and permissions in main admin class

## 1. Custom Post Type Registration

### File: `includes/class-provincial-post-types.php`

```php
private function register_event_post_type() {
    register_post_type('esp_event', array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'capability_type'    => 'post',
        'capabilities'       => array(
            'edit_post'          => 'edit_posts',
            'read_post'          => 'read',
            'delete_post'        => 'delete_posts',
            'edit_posts'         => 'edit_posts',
            'edit_others_posts'  => 'edit_others_posts',
            'publish_posts'      => 'publish_posts',
            'read_private_posts' => 'read_private_posts',
        ),
        'labels'             => array(
            'name'               => 'Events',
            'singular_name'      => 'Event',
            'add_new_item'       => 'Add New Event',
            'edit_item'          => 'Edit Event',
            'all_items'          => 'All Events',
        ),
    ));
}
```

### Key Points:
- Uses standard WordPress post capabilities
- Supports title, editor, thumbnail, and excerpt
- Public post type but with custom admin interface

## 2. Database Schema

### Core Post Data (wp_posts table):
- `post_title`: Event title
- `post_content`: Event description
- `post_status`: publish, draft, private
- `post_type`: 'esp_event'
- `post_author`: Creator user ID

### Meta Fields (wp_postmeta table):
- `_esp_event_start_date`: Event start date (YYYY-MM-DD)
- `_esp_event_end_date`: Event end date (YYYY-MM-DD)
- `_esp_event_location`: Event location
- `_esp_event_contact`: Contact information
- `_esp_district_id`: Linked district ID (foreign key to esp_district posts)

## 3. Menu Integration

### File: `includes/class-provincial-admin.php`

```php
// Events submenu
if (current_user_can('manage_provincial_events') || current_user_can('manage_district_events') || current_user_can('manage_options')) {
    $events_capability = current_user_can('manage_options') ? 'manage_options' : 'manage_district_events';
    add_submenu_page(
        'provincial-admin-dashboard',
        __('Events', 'esp-admin-manager'),
        __('Events', 'esp-admin-manager'),
        $events_capability,
        'provincial-admin-events',
        array($this, 'events_page')
    );
}

public function events_page() {
    include PROVINCIAL_ADMIN_MANAGER_PLUGIN_DIR . 'admin/views/events.php';
}
```

### Permission Logic:
- **WP Admin (`manage_options`)**: Full CRUD access to all events
- **District Users**: Can only manage events linked to their assigned districts
- **Provincial Users**: Can manage all events

## 4. Main View File Structure

### File: `admin/views/events.php`

The main view file handles:
1. **Form Processing**: POST request handling and validation
2. **Action Routing**: Determines which view to display (list, create, edit)
3. **CRUD Operations**: Create, read, update, delete functionality
4. **Permission Checks**: User-based access control
5. **UI Rendering**: Forms and data display

### Key Sections:

#### A. Form Processing (Lines 35-98)
```php
// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Nonce verification
    if (!isset($_POST['esp_events_nonce']) || !wp_verify_nonce($_POST['esp_events_nonce'], 'esp_events_action')) {
        add_settings_error('esp_messages', 'nonce_error', __('Security check failed. Please try again.', 'esp-admin-manager'), 'error');
    } else {
        $post_action = sanitize_text_field($_POST['action']);
        
        if ($post_action === 'create' || $post_action === 'update' || $post_action === 'edit') {
            // Handle both 'update' and 'edit' actions as update operations
            $crud_action = ($post_action === 'create') ? 'create' : 'update';
            
            $result = handle_event_crud($crud_action, $_POST);
            if ($result['success']) {
                // Clear cache and redirect
                wp_cache_delete($result['event_id'], 'posts');
                wp_cache_delete($result['event_id'], 'post_meta');
                
                if ($crud_action === 'create') {
                    $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . $result['event_id'] . '&created=1');
                } else {
                    $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . intval($_POST['event_id']) . '&updated=1');
                }
                wp_redirect($redirect_url);
                exit;
            }
        }
    }
}
```

#### B. CRUD Helper Function (Lines 110-233)
```php
function handle_event_crud($action, $data) {
    // 1. User validation and permissions
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');
    
    // 2. Data validation
    if (empty($data['event_title']) || empty($data['event_start_date'])) {
        return array('success' => false, 'message' => __('Title and start date are required.', 'esp-admin-manager'));
    }
    
    // 3. Permission checks for district users
    if (!current_user_can('manage_options')) {
        if ($is_district_user) {
            if (empty($data['event_district'])) {
                return array('success' => false, 'message' => __('District selection is required.', 'esp-admin-manager'));
            }
            
            $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
            if (!in_array(intval($data['event_district']), $assigned_districts)) {
                return array('success' => false, 'message' => __('You can only create events for your assigned districts.', 'esp-admin-manager'));
            }
        }
    }
    
    // 4. Prepare post data
    $post_data = array(
        'post_title' => sanitize_text_field($data['event_title']),
        'post_content' => wp_kses_post($data['event_content']),
        'post_status' => sanitize_text_field($data['event_status']),
        'post_type' => 'esp_event'
    );
    
    // 5. Create or update post
    if ($action === 'create') {
        $post_data['post_author'] = $current_user_id;
        $event_id = wp_insert_post($post_data);
        $message = __('Event created successfully!', 'esp-admin-manager');
    } else {
        $post_data['ID'] = intval($data['event_id']);
        
        // Verify post exists and is correct type
        $existing_post = get_post($post_data['ID']);
        if (!$existing_post || $existing_post->post_type !== 'esp_event') {
            return array('success' => false, 'message' => __('Event not found.', 'esp-admin-manager'));
        }
        
        $event_id = wp_update_post($post_data, true);
        $message = __('Event updated successfully!', 'esp-admin-manager');
    }
    
    // 6. Error handling
    if (is_wp_error($event_id) || !$event_id) {
        return array('success' => false, 'message' => __('Failed to save event.', 'esp-admin-manager'));
    }
    
    // 7. Save meta fields
    update_post_meta($event_id, '_esp_event_start_date', sanitize_text_field($data['event_start_date']));
    update_post_meta($event_id, '_esp_event_end_date', sanitize_text_field($data['event_end_date']));
    update_post_meta($event_id, '_esp_event_location', sanitize_text_field($data['event_location']));
    update_post_meta($event_id, '_esp_event_contact', sanitize_textarea_field($data['event_contact']));
    update_post_meta($event_id, '_esp_district_id', intval($data['event_district']));
    
    // 8. Handle featured image
    if (!empty($data['event_image_id'])) {
        set_post_thumbnail($event_id, intval($data['event_image_id']));
    } else {
        delete_post_thumbnail($event_id);
    }
    
    return array('success' => true, 'message' => $message, 'event_id' => $event_id);
}
```

## 5. Data Retrieval and Filtering

### User-Based Data Access:

```php
// Get events based on user type
if ($is_district_user && !current_user_can('manage_options')) {
    // District users see only events from their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        // Get all events first, then filter by district assignment
        $all_events = get_posts(array(
            'post_type' => 'esp_event',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        ));

        // Filter events by assigned districts
        $events = array();
        foreach ($all_events as $event) {
            $event_district_id = get_post_meta($event->ID, '_esp_district_id', true);
            if (!empty($event_district_id) && in_array($event_district_id, $assigned_districts)) {
                $events[] = $event;
            }
        }
    } else {
        $events = array(); // No assigned districts
    }
} else {
    // Provincial users and administrators see all events
    $events = get_posts(array(
        'post_type' => 'esp_event',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'meta_value',
        'meta_key' => '_esp_event_start_date',
        'order' => 'ASC'
    ));
}
```

## 6. Form Implementation

### A. Form Structure:

```php
<form method="post" action="<?php echo esc_url(admin_url('admin.php?page=provincial-admin-events')); ?>">
    <?php wp_nonce_field('esp_events_action', 'esp_events_nonce'); ?>
    <input type="hidden" name="action" value="<?php echo $mode; ?>">
    <?php if ($mode === 'edit'): ?>
        <input type="hidden" name="event_id" value="<?php echo $event_id; ?>">
    <?php endif; ?>

    <!-- Form fields here -->
</form>
```

### B. Key Form Fields:

1. **Text Input Example:**
```php
<input type="text" id="event_title" name="event_title"
       value="<?php echo esc_attr($event_data['title']); ?>" required>
```

2. **Date Input Example:**
```php
<input type="date" id="event_start_date" name="event_start_date"
       value="<?php echo esc_attr($event_data['start_date']); ?>" required>
```

3. **Select Dropdown Example:**
```php
<select id="event_district" name="event_district">
    <option value=""><?php _e('All Districts (Optional)', 'esp-admin-manager'); ?></option>
    <?php foreach ($available_districts as $district): ?>
        <option value="<?php echo esc_attr($district->ID); ?>"
                <?php selected($event_data['district_id'], $district->ID); ?>>
            <?php echo esc_html($district->post_title); ?>
        </option>
    <?php endforeach; ?>
</select>
```

4. **Textarea Example:**
```php
<textarea id="event_content" name="event_content" rows="6"
          placeholder="<?php _e('Provide detailed information about the event...', 'esp-admin-manager'); ?>"><?php echo esc_textarea($event_data['content']); ?></textarea>
```

## 7. Data Loading for Edit Mode

### Loading Event Data:

```php
function display_event_form($mode, $event_id = 0) {
    // Initialize default data
    $event_data = array(
        'title' => '',
        'content' => '',
        'start_date' => '',
        'end_date' => '',
        'location' => '',
        'contact' => '',
        'district_id' => '',
        'status' => 'publish',
        'image_id' => ''
    );

    // Load existing data for edit mode
    if ($mode === 'edit' && $event_id) {
        // Clear cache to ensure fresh data
        wp_cache_delete($event_id, 'posts');
        wp_cache_delete($event_id, 'post_meta');

        $event = get_post($event_id);
        if ($event) {
            $event_data = array(
                'title' => $event->post_title,
                'content' => $event->post_content,
                'start_date' => get_post_meta($event_id, '_esp_event_start_date', true),
                'end_date' => get_post_meta($event_id, '_esp_event_end_date', true),
                'location' => get_post_meta($event_id, '_esp_event_location', true),
                'contact' => get_post_meta($event_id, '_esp_event_contact', true),
                'district_id' => get_post_meta($event_id, '_esp_district_id', true),
                'status' => $event->post_status,
                'image_id' => get_post_thumbnail_id($event_id)
            );
        }
    }
}
```

## 8. List View Implementation

### Events Table Display:

```php
<table style="width: 100%; border-collapse: collapse;">
    <thead>
        <tr>
            <th><?php _e('Title', 'esp-admin-manager'); ?></th>
            <th><?php _e('Date', 'esp-admin-manager'); ?></th>
            <th><?php _e('Location', 'esp-admin-manager'); ?></th>
            <th><?php _e('District', 'esp-admin-manager'); ?></th>
            <th><?php _e('Status', 'esp-admin-manager'); ?></th>
            <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
        </tr>
    </thead>
    <tbody>
        <?php foreach ($events as $event):
            $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
            $district_id = get_post_meta($event->ID, '_esp_district_id', true);

            // Get district name
            $district_name = '';
            if ($district_id) {
                $district = get_post($district_id);
                if ($district) {
                    $district_name = $district->post_title;
                }
            }
        ?>
        <tr>
            <td><?php echo esc_html($event->post_title); ?></td>
            <td><?php echo $start_date ? esc_html(date('M j, Y', strtotime($start_date))) : '—'; ?></td>
            <td><?php echo esc_html(get_post_meta($event->ID, '_esp_event_location', true)); ?></td>
            <td><?php echo $district_name ? esc_html($district_name) : __('All Districts', 'esp-admin-manager'); ?></td>
            <td><?php echo esc_html(ucfirst($event->post_status)); ?></td>
            <td>
                <a href="?page=provincial-admin-events&action=edit&event_id=<?php echo $event->ID; ?>">
                    <?php _e('Edit', 'esp-admin-manager'); ?>
                </a>
                <a href="?page=provincial-admin-events&action=delete&event_id=<?php echo $event->ID; ?>&_wpnonce=<?php echo wp_create_nonce('delete_event_' . $event->ID); ?>"
                   onclick="return confirm('<?php _e('Are you sure?', 'esp-admin-manager'); ?>')">
                    <?php _e('Delete', 'esp-admin-manager'); ?>
                </a>
            </td>
        </tr>
        <?php endforeach; ?>
    </tbody>
</table>
```

## 9. Delete Functionality

### Delete Handler:

```php
function handle_event_delete($event_id) {
    $current_user_id = get_current_user_id();
    $user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
    $is_district_user = ($user_type === 'district');

    // Check permissions for district users
    if ($is_district_user && !current_user_can('manage_options')) {
        $event_district_id = get_post_meta($event_id, '_esp_district_id', true);
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

        if (!in_array($event_district_id, $assigned_districts)) {
            return array('success' => false, 'message' => __('You can only delete events from your assigned districts.', 'esp-admin-manager'));
        }
    }

    $result = wp_delete_post($event_id, true);

    if ($result) {
        return array('success' => true, 'message' => __('Event deleted successfully!', 'esp-admin-manager'));
    } else {
        return array('success' => false, 'message' => __('Failed to delete event.', 'esp-admin-manager'));
    }
}
```

### Delete Action Handling:

```php
// Handle delete action
if ($action === 'delete' && $event_id && wp_verify_nonce($_GET['_wpnonce'], 'delete_event_' . $event_id)) {
    $result = handle_event_delete($event_id);
    if ($result['success']) {
        add_settings_error('esp_messages', 'event_deleted', $result['message'], 'success');
        $action = '';
        $event_id = 0;
    } else {
        add_settings_error('esp_messages', 'event_delete_error', $result['message'], 'error');
    }
}
```

## 10. Security Implementation

### A. Nonce Protection:
```php
// Generate nonce in form
<?php wp_nonce_field('esp_events_action', 'esp_events_nonce'); ?>

// Verify nonce on submission
if (!wp_verify_nonce($_POST['esp_events_nonce'], 'esp_events_action')) {
    add_settings_error('esp_messages', 'nonce_error', __('Security check failed.', 'esp-admin-manager'), 'error');
}
```

### B. Data Sanitization:
```php
$post_data = array(
    'post_title' => sanitize_text_field($data['event_title']),
    'post_content' => wp_kses_post($data['event_content']),
    'post_status' => sanitize_text_field($data['event_status']),
    'post_type' => 'esp_event'
);

// Meta fields
update_post_meta($event_id, '_esp_event_start_date', sanitize_text_field($data['event_start_date']));
update_post_meta($event_id, '_esp_event_contact', sanitize_textarea_field($data['event_contact']));
update_post_meta($event_id, '_esp_district_id', intval($data['event_district']));
```

### C. Permission Checks:
```php
// Menu capability check
if (current_user_can('manage_provincial_events') || current_user_can('manage_district_events') || current_user_can('manage_options')) {
    // Add menu
}

// Operation-level permission check
if (!current_user_can('manage_options')) {
    if ($is_district_user) {
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!in_array(intval($data['event_district']), $assigned_districts)) {
            return array('success' => false, 'message' => __('Permission denied.', 'esp-admin-manager'));
        }
    }
}
```

## 11. Success/Error Handling

### A. Success Messages:
```php
// Show success messages for redirected updates
if (isset($_GET['updated']) && $_GET['updated'] == '1' && $event_id) {
    add_settings_error('esp_messages', 'event_updated', __('Event updated successfully!', 'esp-admin-manager'), 'success');
}
if (isset($_GET['created']) && $_GET['created'] == '1' && $event_id) {
    add_settings_error('esp_messages', 'event_created', __('Event created successfully!', 'esp-admin-manager'), 'success');
}

// Display messages
<?php settings_errors('esp_messages'); ?>
```

### B. Redirect After Operations:
```php
if ($result['success']) {
    // Clear cache
    wp_cache_delete($result['event_id'], 'posts');
    wp_cache_delete($result['event_id'], 'post_meta');

    if ($crud_action === 'create') {
        $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . $result['event_id'] . '&created=1');
    } else {
        $redirect_url = admin_url('admin.php?page=provincial-admin-events&action=edit&event_id=' . intval($_POST['event_id']) . '&updated=1');
    }

    // Redirect with fallback
    if (!headers_sent()) {
        wp_redirect($redirect_url);
        exit;
    } else {
        echo '<script>window.location.href = "' . esc_js($redirect_url) . '";</script>';
        exit;
    }
}
```

## 12. Best Practices Implemented

### A. Code Organization:
- **Separation of Concerns**: CRUD logic separated from display logic
- **Reusable Functions**: `handle_event_crud()` and `handle_event_delete()`
- **Consistent Naming**: All functions and variables follow consistent naming conventions

### B. WordPress Standards:
- **Hooks and Filters**: Uses WordPress action and filter hooks
- **Internationalization**: All strings wrapped in `__()` or `_e()` functions
- **Sanitization**: All input data properly sanitized
- **Nonce Security**: All forms protected with nonces

### C. User Experience:
- **Clear Feedback**: Success and error messages for all operations
- **Intuitive Interface**: Clean, professional admin interface
- **Permission-Based Access**: Users see only what they can access
- **Responsive Design**: Interface works on different screen sizes

### D. Performance:
- **Cache Management**: Clears relevant caches after updates
- **Efficient Queries**: Uses appropriate WordPress query functions
- **Minimal Database Calls**: Optimized data retrieval

## 13. Implementation Checklist

When implementing a similar feature, follow this checklist:

### Phase 1: Planning
- [ ] Define custom post type requirements
- [ ] Plan meta fields and data structure
- [ ] Design permission system
- [ ] Create wireframes for UI

### Phase 2: Backend Setup
- [ ] Register custom post type
- [ ] Add menu integration
- [ ] Set up permission checks
- [ ] Create CRUD helper functions

### Phase 3: Frontend Implementation
- [ ] Create main view file
- [ ] Implement form handling
- [ ] Add data validation
- [ ] Create list and edit views

### Phase 4: Security & Polish
- [ ] Add nonce protection
- [ ] Implement data sanitization
- [ ] Add success/error handling
- [ ] Test all user permission scenarios

### Phase 5: Testing
- [ ] Test CRUD operations
- [ ] Verify permission restrictions
- [ ] Test with different user types
- [ ] Validate data integrity

## 14. Common Pitfalls to Avoid

1. **Cache Issues**: Always clear relevant caches after updates
2. **Permission Bypass**: Don't rely only on UI hiding - validate permissions in backend
3. **Data Validation**: Validate both client-side and server-side
4. **Nonce Expiration**: Handle nonce failures gracefully
5. **Redirect Loops**: Use proper redirect patterns to avoid form resubmission
6. **SQL Injection**: Use WordPress functions instead of raw SQL
7. **XSS Vulnerabilities**: Always escape output data

## 15. Extension Points

The Events feature can be extended with:

- **Custom Fields**: Add more meta fields as needed
- **File Attachments**: Extend image handling to support multiple files
- **Email Notifications**: Add notification system for event updates
- **Calendar Integration**: Export events to calendar formats
- **Frontend Display**: Create public-facing event displays
- **Search and Filtering**: Add advanced search capabilities
- **Bulk Operations**: Implement bulk edit/delete functionality

## Conclusion

This implementation provides a solid foundation for CRUD features in WordPress plugins. The pattern can be adapted for other content types by:

1. Changing the post type registration
2. Modifying the meta fields
3. Updating the form fields
4. Adjusting permission logic as needed

The key is maintaining the separation of concerns, following WordPress standards, and implementing proper security measures throughout the feature.
