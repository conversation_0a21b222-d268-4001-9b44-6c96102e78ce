# News Feature Implementation Guide
## Dakoii Provincial Administration Manager Plugin

### Overview
The News Feature is a comprehensive content management system that allows provincial and district administrators to create, manage, and display news articles with role-based access control and district-specific filtering.

---

## Architecture Overview

### Core Components
1. **Custom Post Type**: `esp_news`
2. **Meta Fields**: Date, source, featured status, district assignment
3. **Admin Interface**: Management dashboard with statistics
4. **Frontend Display**: Multiple shortcodes with styling
5. **User Access Control**: District-based filtering and permissions
6. **Single Post Enhancement**: Automatic content enhancement

---

## 1. Custom Post Type Registration

**File**: `includes/class-provincial-post-types.php`

```php
private function register_news_post_type() {
    register_post_type('esp_news', array(
        'public'             => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'supports'           => array('title', 'editor', 'thumbnail', 'excerpt'),
        'capability_type'    => 'post',
        'capabilities'       => array(
            'edit_post'          => 'edit_posts',
            'read_post'          => 'read',
            'delete_post'        => 'delete_posts',
            'edit_posts'         => 'edit_posts',
            'edit_others_posts'  => 'edit_others_posts',
            'publish_posts'      => 'publish_posts',
            'read_private_posts' => 'read_private_posts',
        ),
        'labels'             => array(
            'name'               => 'News',
            'singular_name'      => 'News Article',
            'add_new_item'       => 'Add New News Article',
            'edit_item'          => 'Edit News Article',
            'all_items'          => 'All News',
        ),
    ));
}
```

**Features**:
- Standard WordPress post capabilities
- Supports title, editor, thumbnail, and excerpt
- Integrated with WordPress admin menu

---

## 2. Meta Fields System

**File**: `includes/class-provincial-meta-boxes.php`

### Meta Box Registration
```php
add_meta_box(
    'provincial_news_details',
    __('News Details', 'esp-admin-manager'),
    array($this, 'news_meta_box_callback'),
    'esp_news',
    'normal',
    'high'
);
```

### Meta Fields
- `_esp_news_date`: Publication date
- `_esp_news_source`: News source/department
- `_esp_news_featured`: Featured status (1/0)
- `_esp_district_id`: Associated district ID

### District-Based Access Control
```php
// District users can only assign news to their assigned districts
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');

if ($is_district_user) {
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
    // Show only assigned districts in dropdown
}
```

---

## 3. Admin Dashboard Interface

**File**: `admin/views/news.php`

### Key Features
1. **Role-Based Content Filtering**
2. **Quick Actions Panel**
3. **News Statistics Dashboard**
4. **Live Preview Section**
5. **Management Guidelines**

### District User Filtering
```php
if ($is_district_user && !current_user_can('manage_options')) {
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
    
    if (!empty($assigned_districts)) {
        $all_news = get_posts(array(
            'post_type' => 'esp_news',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'date',
            'order' => 'DESC'
        ));

        // Filter news by assigned districts
        $news = array();
        foreach ($all_news as $news_item) {
            $news_district_id = get_post_meta($news_item->ID, '_esp_district_id', true);
            if (!empty($news_district_id) && in_array($news_district_id, $assigned_districts)) {
                $news[] = $news_item;
            }
        }
    }
}
```

### Statistics Display
- Total Articles Count
- Featured Articles Count
- Recent Articles List
- District-specific filtering

---

## 4. Frontend Display System

### Shortcode Registration
**File**: `includes/class-provincial-shortcodes.php`

#### Multiple Naming Conventions
```php
// Primary shortcodes (recommended)
add_shortcode('dakoii_prov_admin_news', array($this, 'news_shortcode'));

// Alternative shortcodes
add_shortcode('dakoii_news', array($this, 'news_shortcode'));

// Legacy shortcodes (backward compatibility)
add_shortcode('esp_news', array($this, 'news_shortcode'));
```

#### Shortcode Parameters
```php
$atts = shortcode_atts(array(
    'limit' => 5,                    // Number of articles to show
    'featured_only' => 'false',      // Show only featured articles
    'show_images' => 'true',         // Display featured images
    'level' => 'district',           // Access level
    'district_id' => ''              // Specific district filter
), $atts);
```

### Usage Examples
```html
<!-- Basic usage -->
[dakoii_prov_admin_news]

<!-- Show only 3 articles -->
[dakoii_prov_admin_news limit="3"]

<!-- Show only featured articles -->
[dakoii_prov_admin_news featured_only="true"]

<!-- Hide images -->
[dakoii_prov_admin_news show_images="false"]
```

---

## 5. Frontend Styling

**File**: `public/css/public-style.css`

### News Grid Layout
```css
.esp-news-section {
    padding: 2rem 0;
    background: white;
}

.esp-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}
```

### News Card Styling
```css
.esp-news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--png-green);
}

.esp-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    border-left-color: var(--png-red);
}
```

### PNG Color Scheme Integration
- **Primary Green**: `var(--png-green)`
- **Accent Red**: `var(--png-red)`
- **Highlight Yellow**: `var(--png-yellow)`
- **Background**: `var(--light-gray)`

---

## 6. Single Post Enhancement

**File**: `includes/class-provincial-frontend.php`

### Automatic Content Enhancement
```php
public function enhance_single_news_content($content) {
    if (!is_singular('esp_news') || !in_the_loop() || !is_main_query()) {
        return $content;
    }

    global $post;
    
    $news_date = get_post_meta($post->ID, '_esp_news_date', true);
    $source = get_post_meta($post->ID, '_esp_news_source', true);
    $featured = get_post_meta($post->ID, '_esp_news_featured', true);
    
    // Add meta information and styling to single news posts
    return $enhanced_content;
}
```

### Features
- Automatic meta information display
- Featured image handling
- Source attribution
- Publication date formatting
- Featured article highlighting

---

## 7. Permission System

### Access Control Levels
1. **Provincial Users**: See all news articles
2. **District Users**: See only news from assigned districts
3. **Administrators**: Full access to all content

### Implementation
```php
// Check permissions in shortcode
$district_id = !empty($atts['district_id']) ? intval($atts['district_id']) : null;
if (!$this->check_shortcode_permission('news', $atts['level'], $district_id)) {
    return '<p>' . __('You do not have permission to view this content.', 'esp-admin-manager') . '</p>';
}

// Filter content by user access
$news = $this->filter_content_by_district_access($news);
```

---

## 8. Data Structure

### Database Schema
```sql
-- Post meta fields for esp_news post type
_esp_news_date          VARCHAR(255)  -- Publication date
_esp_news_source        VARCHAR(255)  -- News source/department  
_esp_news_featured      VARCHAR(1)    -- Featured status (1/0)
_esp_district_id        INT(11)       -- Associated district ID
```

### Data Flow
1. **Creation**: Admin creates news with meta fields
2. **Storage**: WordPress posts table + meta fields
3. **Retrieval**: Query with meta_query for filtering
4. **Display**: Shortcode renders with styling
5. **Access Control**: User role-based filtering

---

## 9. Integration Points

### WordPress Integration
- Uses standard WordPress post system
- Integrates with media library for images
- Supports WordPress capabilities system
- Compatible with WordPress themes

### Plugin Integration
- Connects with user role management system
- Integrates with district management
- Uses plugin's styling framework
- Shares permission system with other features

---

## 10. Best Practices

### Content Management
1. Use clear, informative headlines
2. Include source department for credibility
3. Use excerpt field for summaries
4. Mark important news as "Featured"
5. Assign appropriate district associations

### Technical Implementation
1. Always use role-based access control
2. Sanitize all user inputs
3. Use WordPress nonces for security
4. Follow plugin naming conventions
5. Maintain backward compatibility

### Performance Optimization
1. Limit query results with numberposts
2. Use appropriate post_status filters
3. Cache shortcode output when possible
4. Optimize images for web display
5. Use efficient meta_query structures

---

## 11. Troubleshooting

### Common Issues
1. **News not displaying**: Check user permissions and district assignments
2. **Images not showing**: Verify featured image support in theme
3. **Shortcode not working**: Confirm shortcode name and parameters
4. **Access denied**: Check user role and district assignments
5. **Styling issues**: Verify CSS file loading and theme compatibility

### Debug Steps
1. Check WordPress error logs
2. Verify post type registration
3. Confirm meta field values
4. Test with different user roles
5. Validate shortcode parameters

---

## 12. Future Enhancements

### Potential Improvements
1. **Categories/Tags**: Add news categorization
2. **Search Functionality**: Implement news search
3. **RSS Feeds**: Generate news RSS feeds
4. **Email Notifications**: Notify users of new articles
5. **Social Sharing**: Add social media integration
6. **Archive Pages**: Create news archive templates
7. **Related Articles**: Show related news content
8. **Comment System**: Enable article comments

---

This guide provides a complete overview of the News Feature implementation in the Dakoii Provincial Administration Manager plugin, covering all aspects from database structure to frontend display and user permissions.
