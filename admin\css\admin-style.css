/* Provincial Administration Manager - Admin Styles */

:root {
    --esp-primary: #006A4E;
    --esp-secondary: #CE1126;
    --esp-accent: #FFD700;
    --esp-light: #f8fafc;
    --esp-dark: #004d3a;
}

/* Admin Page Header */
.esp-admin-header {
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    color: white;
    padding: 20px;
    margin: 0 -20px 20px -20px;
    border-radius: 8px;
}

.esp-admin-header h1 {
    margin: 0;
    font-size: 28px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.esp-admin-header h1::before {
    content: '🏛️';
    font-size: 32px;
}

.esp-admin-header p {
    margin: 10px 0 0 0;
    opacity: 0.9;
    font-size: 16px;
}

/* Dashboard Cards */
.esp-dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.esp-dashboard-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.esp-dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.esp-dashboard-card-icon {
    font-size: 48px;
    margin-bottom: 15px;
    display: block;
}

.esp-dashboard-card h3 {
    margin: 0 0 10px 0;
    color: var(--esp-primary);
    font-size: 18px;
}

.esp-dashboard-card .count {
    font-size: 32px;
    font-weight: bold;
    color: var(--esp-secondary);
    margin-bottom: 10px;
}

.esp-dashboard-card .description {
    color: #666;
    font-size: 14px;
    margin-bottom: 15px;
}

.esp-dashboard-card .button {
    background: var(--esp-primary);
    color: white;
    text-decoration: none;
    padding: 8px 16px;
    border-radius: 4px;
    display: inline-block;
    transition: background 0.3s ease;
}

.esp-dashboard-card .button:hover {
    background: var(--esp-dark);
    color: white;
}

/* Form Styles */
.esp-form-section {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.esp-form-section h3 {
    margin: 0 0 20px 0;
    color: var(--esp-primary);
    border-bottom: 2px solid var(--esp-accent);
    padding-bottom: 10px;
}

.esp-form-row {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 15px;
    margin-bottom: 20px;
    align-items: start;
}

.esp-form-row label {
    font-weight: 600;
    color: #333;
    padding-top: 5px;
}

.esp-form-row input,
.esp-form-row textarea,
.esp-form-row select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.esp-form-row input:focus,
.esp-form-row textarea:focus,
.esp-form-row select:focus {
    border-color: var(--esp-primary);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 106, 78, 0.1);
}

.esp-form-row .description {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
    font-style: italic;
}

/* Statistics Grid */
.esp-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.esp-stats-item {
    background: var(--esp-light);
    padding: 15px;
    border-radius: 6px;
    border-left: 4px solid var(--esp-primary);
}

.esp-stats-item label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--esp-primary);
}

.esp-stats-item input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
    font-size: 16px;
    font-weight: bold;
}

.esp-stats-value {
    display: block;
    font-size: 24px;
    font-weight: bold;
    color: var(--esp-primary);
    text-align: center;
    margin-top: 5px;
}

/* List Tables */
.esp-list-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.esp-list-table th {
    background: var(--esp-primary);
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 600;
}

.esp-list-table td {
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.esp-list-table tr:hover {
    background: var(--esp-light);
}

.esp-list-table .actions {
    white-space: nowrap;
}

.esp-list-table .actions a {
    margin-right: 10px;
    text-decoration: none;
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
}

.esp-list-table .actions .edit {
    background: #0073aa;
    color: white;
}

.esp-list-table .actions .delete {
    background: #dc3232;
    color: white;
}

/* Buttons */
.esp-button {
    background: var(--esp-primary);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.esp-button:hover {
    background: var(--esp-dark);
    color: white;
}

.esp-button.secondary {
    background: var(--esp-secondary);
}

.esp-button.secondary:hover {
    background: #a00e1f;
}

.esp-button.large {
    padding: 15px 30px;
    font-size: 16px;
}

/* Messages */
.esp-message {
    padding: 12px 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.esp-message.success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.esp-message.error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.esp-message.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Tabs */
.esp-tabs {
    margin-bottom: 20px;
}

.esp-tab-nav {
    display: flex;
    border-bottom: 2px solid #ddd;
    margin-bottom: 20px;
}

.esp-tab-nav button {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    color: #666;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.esp-tab-nav button.active {
    color: var(--esp-primary);
    border-bottom-color: var(--esp-primary);
}

.esp-tab-nav button:hover {
    color: var(--esp-primary);
}

.esp-tab-content {
    display: none;
}

.esp-tab-content.active {
    display: block;
}

/* Media Upload */
.esp-media-upload {
    border: 2px dashed #ddd;
    padding: 40px;
    text-align: center;
    border-radius: 8px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.esp-media-upload:hover {
    border-color: var(--esp-primary);
    background: var(--esp-light);
}

.esp-media-upload.has-image {
    border-style: solid;
    border-color: var(--esp-primary);
}

.esp-media-preview {
    max-width: 200px;
    max-height: 200px;
    margin: 0 auto 15px;
    border-radius: 8px;
    overflow: hidden;
}

.esp-media-preview img {
    width: 100%;
    height: auto;
    display: block;
}

/* Responsive */
@media (max-width: 768px) {
    .esp-dashboard-cards {
        grid-template-columns: 1fr;
    }
    
    .esp-form-row {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .esp-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .esp-tab-nav {
        flex-wrap: wrap;
    }
    
    .esp-list-table {
        font-size: 12px;
    }
    
    .esp-list-table th,
    .esp-list-table td {
        padding: 8px;
    }
}

/* Loading States */
.esp-loading {
    opacity: 0.6;
    pointer-events: none;
}

.esp-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--esp-primary);
    border-radius: 50%;
    animation: esp-spin 1s linear infinite;
}

@keyframes esp-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Help Text */
.esp-help {
    background: #e7f3ff;
    border: 1px solid #b8daff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 20px;
}

.esp-help h4 {
    margin: 0 0 10px 0;
    color: #004085;
}

.esp-help p {
    margin: 0;
    color: #004085;
    font-size: 14px;
}

/* Quick Actions */
.esp-quick-actions {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.esp-quick-actions h3 {
    margin: 0 0 15px 0;
    color: var(--esp-primary);
}

.esp-quick-actions .actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.esp-quick-actions .actions a {
    background: var(--esp-light);
    color: var(--esp-primary);
    text-decoration: none;
    padding: 8px 15px;
    border-radius: 4px;
    border: 1px solid #ddd;
    transition: all 0.3s ease;
}

.esp-quick-actions .actions a:hover {
    background: var(--esp-primary);
    color: white;
}

/* Slideshow Management Styles */
.esp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-modal-content {
    background: white;
    border-radius: 8px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.esp-modal-large .esp-modal-content {
    max-width: 900px;
}

.esp-modal-small .esp-modal-content {
    max-width: 400px;
}

.esp-modal-header {
    padding: 20px;
    border-bottom: 1px solid #ddd;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--esp-light);
}

.esp-modal-header h3 {
    margin: 0;
    color: var(--esp-primary);
}

.esp-modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.esp-modal-close:hover {
    background: #f0f0f0;
    color: #333;
}

.esp-modal-body {
    padding: 20px;
}

.esp-modal-footer {
    padding: 20px;
    border-top: 1px solid #ddd;
    background: var(--esp-light);
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

/* Slideshow specific styles */
.esp-tag {
    display: inline-block;
    background: var(--esp-light);
    color: var(--esp-primary);
    padding: 3px 8px;
    margin: 2px;
    border-radius: 12px;
    font-size: 11px;
    border: 1px solid #ddd;
}

.shortcode-display {
    background: #f1f1f1;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    border: 1px solid #ddd;
    display: inline-block;
}

.copy-shortcode {
    margin-left: 8px;
    font-size: 11px;
    padding: 2px 6px;
}

.slide-count {
    font-weight: bold;
    color: var(--esp-primary);
}

.slide-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 6px;
    background: #fafafa;
    transition: all 0.3s ease;
}

.slide-item:hover {
    background: #f0f0f0;
    border-color: var(--esp-primary);
}

.slide-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slide-preview img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    border: 1px solid #ddd;
}

.slide-info {
    flex: 1;
}

.slide-info strong {
    color: var(--esp-primary);
    display: block;
    margin-bottom: 5px;
}

.slide-info p {
    margin: 0 0 5px 0;
    color: #666;
    font-size: 13px;
}

.slide-info small {
    color: #999;
    font-size: 11px;
}

.slide-actions {
    display: flex;
    gap: 8px;
    flex-direction: column;
}

.slide-actions .button {
    font-size: 11px;
    padding: 4px 8px;
    height: auto;
    line-height: 1.2;
}

.button-delete {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

.button-delete:hover {
    background: #c82333 !important;
    border-color: #bd2130 !important;
}

/* Form enhancements for slideshow */
.esp-form-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ddd;
}

.esp-tags-help {
    margin-top: 15px;
    padding: 15px;
    background: var(--esp-light);
    border-left: 4px solid var(--esp-primary);
    border-radius: 4px;
}

.esp-tags-help h4 {
    margin-top: 0;
    margin-bottom: 10px;
    color: var(--esp-primary);
}

.esp-tags-help ul {
    margin: 0;
    padding-left: 20px;
}

.esp-tags-help li {
    margin-bottom: 5px;
    font-size: 13px;
}

.esp-help-section {
    margin-top: 30px;
    padding: 20px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.esp-help-section h3 {
    margin-top: 0;
    color: var(--esp-primary);
    border-bottom: 2px solid var(--esp-accent);
    padding-bottom: 10px;
}

.esp-help-section h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    color: var(--esp-dark);
}

.esp-help-section ul {
    padding-left: 20px;
}

.esp-help-section li {
    margin-bottom: 8px;
    line-height: 1.5;
}

.esp-help-section code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: 'Courier New', monospace;
    border: 1px solid #e9ecef;
    color: var(--esp-primary);
}

.required {
    color: var(--esp-secondary);
    font-weight: bold;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .esp-modal-content {
        width: 95%;
        margin: 10px;
    }

    .slide-preview {
        flex-direction: column;
        align-items: flex-start;
    }

    .slide-preview img {
        width: 100%;
        height: 120px;
    }

    .slide-actions {
        flex-direction: row;
        width: 100%;
        justify-content: flex-end;
    }
}

/* Map Meta Box Styles */
#provincial_map_details .inside {
    padding: 15px;
}

#provincial_map_details h3 {
    margin: 0 0 15px 0;
    padding: 10px 0;
    border-bottom: 2px solid var(--esp-primary);
    color: var(--esp-primary);
    font-size: 16px;
    font-weight: 600;
}

.esp-map-current-file {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
}

.esp-map-current-file p {
    margin: 5px 0;
}

.esp-map-current-file .button {
    margin-top: 10px;
}

.esp-map-file-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    padding: 10px;
    color: #856404;
}

.esp-map-upload-section {
    background: #ffffff;
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    transition: border-color 0.3s ease;
}

.esp-map-upload-section:hover,
.esp-map-upload-section.esp-drag-over {
    border-color: var(--esp-primary);
    background-color: #f8fff8;
}

.esp-map-upload-section input[type="file"] {
    margin: 10px 0;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    width: 100%;
    max-width: 400px;
}

.esp-map-shortcode-section {
    background: #f1f3f4;
    border-radius: 6px;
    padding: 15px;
}

.esp-map-shortcode-section code {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 8px 12px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    display: inline-block;
    margin: 5px 5px 5px 0;
}

.copy-shortcode {
    margin-left: 10px;
    vertical-align: middle;
}

.copy-shortcode:hover {
    background-color: var(--esp-primary);
    border-color: var(--esp-primary);
    color: white;
}

/* WordPress Standard List Table Enhancements */
.party-badge {
    background: #f0f0f1;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: #50575e;
    border: 1px solid #dcdcde;
}

.status-published {
    color: #00a32a;
    font-weight: 600;
}

.status-draft {
    color: #dba617;
    font-weight: 600;
}

.status-private {
    color: #d63638;
    font-weight: 600;
}

/* Responsive table improvements */
@media screen and (max-width: 782px) {
    .wp-list-table .column-electorate,
    .wp-list-table .column-party,
    .wp-list-table .column-district,
    .wp-list-table .column-llgs,
    .wp-list-table .column-wards,
    .wp-list-table .column-population,
    .wp-list-table .column-area {
        display: none;
    }
}
