# MPs Custom CRUD Implementation Guide

## Overview

This document outlines the implementation of a custom MPs CRUD system that replaces the WordPress standard post type interface with a custom-styled interface and implements role-based access control.

## Features Implemented

### 1. Custom MPs Controller
- **File**: `includes/class-provincial-mps-controller.php`
- **Purpose**: Handles all CRUD operations for MPs with role-based access control
- **Key Methods**:
  - `get_mps_for_user()` - Gets MPs based on user permissions
  - `user_can_manage_mp()` - Checks if user can manage specific MP
  - `create_mp()` - Creates new MP with validation
  - `update_mp()` - Updates existing MP with permission checks
  - `delete_mp()` - Deletes MP (admin/provincial users only)
  - AJAX handlers for all operations

### 2. Custom Views
- **File**: `admin/views/mps-custom.php`
- **Purpose**: Custom interface replacing WordPress standard post editing
- **Features**:
  - Grid-based MP display with cards
  - Modal-based add/edit forms
  - Role-based UI elements
  - Custom styling throughout

### 3. Custom Styling
- **File**: `admin/css/mps-custom.css`
- **Purpose**: Complete custom styling replacing WordPress defaults
- **Features**:
  - PNG flag colors (Green, Red, Yellow)
  - Modern card-based layout
  - Responsive design
  - Custom buttons and forms
  - Modal dialogs

### 4. JavaScript Functionality
- **File**: `admin/js/mps-custom.js`
- **Purpose**: Handles all frontend interactions
- **Features**:
  - AJAX form submissions
  - Media uploader integration
  - Modal management
  - Real-time notifications
  - Form validation

## Role-Based Access Control

### WP Admin Users (manage_options capability)
- **Full CRUD Access**: Can create, read, update, and delete all MPs
- **See All MPs**: View all MPs regardless of district assignment
- **All Districts**: Can assign MPs to any district

### Provincial Users (provincial_user role)
- **Full CRUD Access**: Can create, read, update, and delete all MPs
- **See All MPs**: View all MPs regardless of district assignment
- **All Districts**: Can assign MPs to any district

### District Users (district_user role)
- **Limited Access**: Can only see and edit MPs assigned to their districts
- **Update Only**: Can update existing MPs but cannot delete them
- **Assigned Districts Only**: Can only assign MPs to their assigned districts
- **Create MPs**: Can create new MPs for their assigned districts

## File Structure

```
dakoii-prov-administration-manager/
├── includes/
│   └── class-provincial-mps-controller.php    # CRUD controller
├── admin/
│   ├── views/
│   │   └── mps-custom.php                      # Custom view
│   ├── css/
│   │   └── mps-custom.css                      # Custom styling
│   └── js/
│       └── mps-custom.js                       # JavaScript functionality
└── dev_guide/
    └── MPs_Custom_CRUD_Implementation.md       # This guide
```

## Testing Instructions

### Prerequisites
1. Ensure the plugin is activated
2. Have test users with different roles:
   - Administrator user
   - Provincial user
   - District user with assigned districts

### Test Cases

#### 1. Administrator User Testing
1. **Login as Administrator**
2. **Navigate to**: Provincial Administration > MPs
3. **Expected**: Should see custom interface with all MPs
4. **Test Add MP**:
   - Click "Add New MP"
   - Fill in all fields
   - Upload photo
   - Select any district
   - Save
   - **Expected**: MP should be created successfully
5. **Test Edit MP**:
   - Click "Edit" on any MP
   - Modify fields
   - Save
   - **Expected**: Changes should be saved
6. **Test Delete MP**:
   - Click "Delete" on any MP
   - Confirm deletion
   - **Expected**: MP should be deleted

#### 2. Provincial User Testing
1. **Login as Provincial User**
2. **Navigate to**: Provincial Administration > MPs
3. **Expected**: Should see custom interface with all MPs
4. **Test CRUD Operations**: Same as Administrator
5. **Expected**: All operations should work

#### 3. District User Testing
1. **Login as District User**
2. **Navigate to**: Provincial Administration > MPs
3. **Expected**: 
   - Should see custom interface
   - Should only see MPs from assigned districts
   - Should see "My District MPs" title
   - Should see district assignment info banner
4. **Test Add MP**:
   - Click "Add New MP"
   - **Expected**: District dropdown should only show assigned districts
   - Fill in fields and save
   - **Expected**: MP should be created for assigned district
5. **Test Edit MP**:
   - Click "Edit" on visible MP
   - Modify fields
   - **Expected**: Should be able to edit and save
6. **Test Delete MP**:
   - **Expected**: Delete button should not be visible
7. **Test Access Control**:
   - Try to access MP from non-assigned district (if any exist)
   - **Expected**: Should not be visible in the list

### Visual Testing

#### 1. Interface Elements
- **Header**: Should show PNG colors (green background)
- **Cards**: Should display in grid layout
- **Photos**: Should display properly or show placeholder
- **Buttons**: Should use custom styling
- **Modal**: Should open/close smoothly

#### 2. Responsive Design
- **Desktop**: Grid layout with multiple columns
- **Tablet**: Fewer columns, responsive layout
- **Mobile**: Single column layout

#### 3. Notifications
- **Success**: Green notifications for successful operations
- **Error**: Red notifications for errors
- **Auto-hide**: Notifications should disappear after 3 seconds

### Error Testing

#### 1. Form Validation
- **Empty Name**: Should show error
- **Invalid District**: District users should get error for non-assigned districts
- **Network Errors**: Should show appropriate error messages

#### 2. Permission Testing
- **Direct Access**: Try accessing MPs via direct URL manipulation
- **AJAX Calls**: Test AJAX endpoints with different user roles
- **Cross-District Access**: District users shouldn't access other districts' MPs

## Troubleshooting

### Common Issues

#### 1. Custom Interface Not Loading
- **Check**: CSS and JS files are enqueued properly
- **Check**: File paths are correct
- **Check**: No JavaScript errors in console

#### 2. AJAX Operations Failing
- **Check**: Nonce verification
- **Check**: User permissions
- **Check**: Network connectivity
- **Check**: WordPress AJAX URL

#### 3. Role-Based Access Not Working
- **Check**: User roles are assigned correctly
- **Check**: District assignments for district users
- **Check**: Capability checks in controller

#### 4. Styling Issues
- **Check**: CSS file is loaded
- **Check**: No conflicting styles from theme
- **Check**: Browser cache cleared

### Debug Mode

To enable debug information for district users, add `?debug=1` to the URL when logged in as an administrator.

## Integration Notes

### 1. Existing Data
- The custom CRUD works with existing MP post data
- No data migration required
- Maintains compatibility with existing shortcodes

### 2. WordPress Integration
- Uses WordPress media uploader
- Follows WordPress coding standards
- Integrates with existing user role system

### 3. Future Enhancements
- Bulk operations
- Advanced filtering
- Export functionality
- Enhanced photo management

## Security Considerations

1. **Nonce Verification**: All AJAX requests use nonces
2. **Capability Checks**: Proper permission checks throughout
3. **Data Sanitization**: All input is sanitized
4. **SQL Injection Prevention**: Uses WordPress functions
5. **XSS Prevention**: Output is escaped properly

## Performance Considerations

1. **Efficient Queries**: Uses optimized WordPress queries
2. **Lazy Loading**: Photos loaded as needed
3. **Minimal AJAX**: Only necessary data transferred
4. **Caching**: Leverages WordPress caching where possible

## Conclusion

The custom MPs CRUD implementation provides a modern, user-friendly interface with proper role-based access control while maintaining full compatibility with the existing WordPress infrastructure and data structure.
