<?php
/**
 * Provincial Slideshow Management Class
 * 
 * Handles slideshow groups and slides functionality
 * 
 * @package Provincial_Administration_Manager
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Provincial Slideshow Class
 */
class Provincial_Slideshow {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        // Constructor is private to prevent direct instantiation
    }
    
    /**
     * Create slideshow group
     */
    public static function create_group($name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';

        // Check if table exists
        $table_check = self::tables_exist();
        if (!$table_check['groups']) {
            // Try to create tables
            self::create_tables();

            // Check again
            $table_check = self::tables_exist();
            if (!$table_check['groups']) {
                return new WP_Error('table_missing', 'Slideshow groups table does not exist and could not be created.');
            }
        }

        $result = $wpdb->insert(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags),
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ),
            array('%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            $error_message = 'Failed to create slideshow group.';
            if ($wpdb->last_error) {
                $error_message .= ' Database error: ' . $wpdb->last_error;
            }
            return new WP_Error('db_error', $error_message);
        }

        return $wpdb->insert_id;
    }
    
    /**
     * Get all slideshow groups
     */
    public static function get_groups() {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';

        // Check if table exists first
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table'") == $table;
        if (!$table_exists) {
            return array();
        }

        $results = $wpdb->get_results("SELECT * FROM $table ORDER BY name ASC");

        return $results ? $results : array();
    }
    
    /**
     * Get single slideshow group
     */
    public static function get_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';
        
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id));
    }
    
    /**
     * Update slideshow group
     */
    public static function update_group($id, $name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';
        
        $result = $wpdb->update(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags),
                'updated_at' => current_time('mysql')
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete slideshow group
     */
    public static function delete_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';
        
        // Delete associated slides first (cascade delete)
        self::delete_slides_by_group($id);
        
        $result = $wpdb->delete($table, array('id' => $id), array('%d'));
        
        return $result !== false;
    }
    
    /**
     * Add slide to group
     */
    public static function add_slide($group_id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        // If no order specified, get next order number
        if ($order == 0) {
            $max_order = $wpdb->get_var($wpdb->prepare(
                "SELECT MAX(slide_order) FROM $table WHERE group_id = %d", 
                $group_id
            ));
            $order = ($max_order !== null) ? $max_order + 1 : 1;
        }
        
        $result = $wpdb->insert(
            $table,
            array(
                'group_id' => intval($group_id),
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order),
                'is_active' => 1,
                'created_at' => current_time('mysql')
            ),
            array('%d', '%s', '%s', '%s', '%s', '%d', '%d', '%s')
        );
        
        if ($result === false) {
            return new WP_Error('db_error', 'Failed to add slide');
        }
        
        return $wpdb->insert_id;
    }
    
    /**
     * Get slides for a group
     */
    public static function get_slides($group_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE group_id = %d AND is_active = 1 ORDER BY slide_order ASC, id ASC", 
            $group_id
        ));
        
        return $results ? $results : array();
    }
    
    /**
     * Get single slide
     */
    public static function get_slide($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id));
    }
    
    /**
     * Update slide
     */
    public static function update_slide($id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        $result = $wpdb->update(
            $table,
            array(
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order)
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%s', '%d'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Delete slide
     */
    public static function delete_slide($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        $result = $wpdb->delete($table, array('id' => $id), array('%d'));
        
        return $result !== false;
    }
    
    /**
     * Delete all slides for a group
     */
    public static function delete_slides_by_group($group_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        return $wpdb->delete($table, array('group_id' => $group_id), array('%d'));
    }
    
    /**
     * Get groups by tag
     */
    public static function get_groups_by_tag($tag) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slideshow_groups';
        
        $results = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE tags LIKE %s ORDER BY name ASC", 
            '%' . $wpdb->esc_like($tag) . '%'
        ));
        
        return $results ? $results : array();
    }
    
    /**
     * Toggle slide active status
     */
    public static function toggle_slide_status($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        $current_status = $wpdb->get_var($wpdb->prepare(
            "SELECT is_active FROM $table WHERE id = %d", 
            $id
        ));
        
        if ($current_status === null) {
            return false;
        }
        
        $new_status = $current_status ? 0 : 1;
        
        $result = $wpdb->update(
            $table,
            array('is_active' => $new_status),
            array('id' => $id),
            array('%d'),
            array('%d')
        );
        
        return $result !== false;
    }
    
    /**
     * Reorder slides
     */
    public static function reorder_slides($slide_orders) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        foreach ($slide_orders as $slide_id => $order) {
            $wpdb->update(
                $table,
                array('slide_order' => intval($order)),
                array('id' => intval($slide_id)),
                array('%d'),
                array('%d')
            );
        }
        
        return true;
    }
    
    /**
     * Get slide count for group
     */
    public static function get_slide_count($group_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'esp_slides';
        
        return $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $table WHERE group_id = %d AND is_active = 1", 
            $group_id
        ));
    }
    
    /**
     * Create database tables
     */
    public static function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Slideshow groups table
        $table_slideshow_groups = $wpdb->prefix . 'esp_slideshow_groups';
        $sql_groups = "CREATE TABLE $table_slideshow_groups (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            tags text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY name_idx (name(191)),
            KEY tags_idx (tags(191))
        ) $charset_collate;";

        // Slides table (removed foreign key constraint for compatibility)
        $table_slides = $wpdb->prefix . 'esp_slides';
        $sql_slides = "CREATE TABLE $table_slides (
            id int(11) NOT NULL AUTO_INCREMENT,
            group_id int(11) NOT NULL,
            title varchar(255),
            description text,
            image_url varchar(500) NOT NULL,
            link_url varchar(500),
            slide_order int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY group_id_idx (group_id),
            KEY order_idx (slide_order),
            KEY active_idx (is_active)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');

        $result1 = dbDelta($sql_groups);
        $result2 = dbDelta($sql_slides);

        // Log results for debugging
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Slideshow tables creation results:');
            error_log('Groups table: ' . print_r($result1, true));
            error_log('Slides table: ' . print_r($result2, true));
        }

        return array('groups' => $result1, 'slides' => $result2);
    }

    /**
     * Check if tables exist
     */
    public static function tables_exist() {
        global $wpdb;

        $table_groups = $wpdb->prefix . 'esp_slideshow_groups';
        $table_slides = $wpdb->prefix . 'esp_slides';

        $groups_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_groups'") == $table_groups;
        $slides_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_slides'") == $table_slides;

        return array(
            'groups' => $groups_exists,
            'slides' => $slides_exists,
            'both' => $groups_exists && $slides_exists
        );
    }

    /**
     * Get last database error
     */
    public static function get_last_error() {
        global $wpdb;
        return $wpdb->last_error;
    }

    /**
     * Debug function to check system status
     */
    public static function debug_status() {
        global $wpdb;

        $status = array();

        // Check table existence
        $tables = self::tables_exist();
        $status['tables'] = $tables;

        // Check database connection
        $status['db_connection'] = !empty($wpdb->dbh);

        // Check table structure if tables exist (suppress any potential output)
        if ($tables['groups']) {
            $status['groups_structure'] = $wpdb->get_results("DESCRIBE " . $wpdb->prefix . "esp_slideshow_groups", ARRAY_A);
        }

        if ($tables['slides']) {
            $status['slides_structure'] = $wpdb->get_results("DESCRIBE " . $wpdb->prefix . "esp_slides", ARRAY_A);
        }

        // Check permissions
        $status['user_can_manage_options'] = current_user_can('manage_options');
        $status['user_can_manage_slideshows'] = current_user_can('manage_provincial_slideshows');

        // Check last error
        $status['last_error'] = $wpdb->last_error;

        return $status;
    }
}
