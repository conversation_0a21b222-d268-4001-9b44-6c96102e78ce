<?php
/**
 * Provincial Administration Manager - Custom Events Management View
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Check user type and get appropriate events
$current_user_id = get_current_user_id();
$user_type = Provincial_User_Roles::get_user_provincial_type($current_user_id);
$is_district_user = ($user_type === 'district');
$is_provincial_user = Provincial_User_Roles::user_has_provincial_access($current_user_id);

// Get events based on user type
if ($is_district_user && !current_user_can('manage_options') && !current_user_can('administrator')) {
    // District users see only events from their assigned districts
    $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);

    if (!empty($assigned_districts)) {
        // Get all events first, then filter by district assignment
        $all_events = get_posts(array(
            'post_type' => 'esp_event',
            'numberposts' => -1,
            'post_status' => 'any',
            'orderby' => 'meta_value',
            'meta_key' => '_esp_event_start_date',
            'order' => 'ASC'
        ));

        // Filter events by assigned districts - only show events linked to assigned districts
        $events = array();
        foreach ($all_events as $event) {
            $event_district_id = get_post_meta($event->ID, '_esp_district_id', true);
            // Only include events that are specifically linked to one of the assigned districts
            if (!empty($event_district_id) && in_array($event_district_id, $assigned_districts)) {
                $events[] = $event;
            }
        }
    } else {
        $events = array(); // No assigned districts
    }
} else {
    // Provincial users, administrators, and anyone with manage_options capability see all events
    $events = get_posts(array(
        'post_type' => 'esp_event',
        'numberposts' => -1,
        'post_status' => 'any',
        'orderby' => 'meta_value',
        'meta_key' => '_esp_event_start_date',
        'order' => 'ASC'
    ));
}
?>

<div class="wrap">
    <?php if ($is_district_user && !current_user_can('manage_options') && !current_user_can('administrator')): ?>
        <h1 class="wp-heading-inline"><?php _e('My District Events', 'esp-admin-manager'); ?></h1>
        <hr class="wp-header-end">

        <?php
        $assigned_districts = Provincial_User_Roles::get_user_assigned_districts($current_user_id);
        if (!empty($assigned_districts)):
            $district_names = array();
            foreach ($assigned_districts as $district_id) {
                $district = get_post($district_id);
                if ($district) {
                    $district_names[] = $district->post_title;
                }
            }
            if (!empty($district_names)): ?>
                <div class="notice notice-info">
                    <p><strong><?php _e('Managing events for:', 'esp-admin-manager'); ?></strong>
                    <?php echo esc_html(implode(', ', $district_names)); ?></p>
                </div>
            <?php endif;
        endif; ?>
    <?php else: ?>
        <h1 class="wp-heading-inline"><?php _e('Events Management', 'esp-admin-manager'); ?></h1>
        <a href="<?php echo admin_url('post-new.php?post_type=esp_event'); ?>" class="page-title-action"><?php _e('Add New', 'esp-admin-manager'); ?></a>
        <hr class="wp-header-end">
    <?php endif; ?>

    <?php settings_errors('esp_messages'); ?>

    <?php if (!empty($events)): ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-title column-primary"><?php _e('Event Title', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Date(s)', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Location', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('District', 'esp-admin-manager'); ?></th>
                    <th scope="col" class="manage-column"><?php _e('Status', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($events as $event):
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    $end_date = get_post_meta($event->ID, '_esp_event_end_date', true);
                    $location = get_post_meta($event->ID, '_esp_event_location', true);
                    $district_id = get_post_meta($event->ID, '_esp_district_id', true);
                    
                    // Get district name
                    $district_name = '';
                    if ($district_id) {
                        $district = get_post($district_id);
                        if ($district && $district->post_status === 'publish') {
                            $district_name = $district->post_title;
                        }
                    }
                    
                    // Determine if event is upcoming, current, or past
                    $today = date('Y-m-d');
                    $event_status = 'past';
                    if ($start_date >= $today) {
                        $event_status = 'upcoming';
                    } elseif ($end_date && $end_date >= $today) {
                        $event_status = 'current';
                    }
                ?>
                <tr>
                    <td class="title column-title has-row-actions column-primary" data-colname="Event Title">
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <?php if (has_post_thumbnail($event->ID)): ?>
                                <?php echo get_the_post_thumbnail($event->ID, array(40, 40), array('style' => 'border-radius: 4px;')); ?>
                            <?php else: ?>
                                <div style="width: 40px; height: 40px; background: #0073aa; border-radius: 4px; display: flex; align-items: center; justify-content: center; color: white; font-size: 16px;">
                                    📅
                                </div>
                            <?php endif; ?>
                            <div>
                                <strong>
                                    <a href="<?php echo get_edit_post_link($event->ID); ?>" class="row-title">
                                        <?php echo esc_html($event->post_title); ?>
                                    </a>
                                </strong>
                                <?php if ($event->post_content): ?>
                                    <div style="font-size: 12px; color: #666; margin-top: 2px;">
                                        <?php echo esc_html(wp_trim_words($event->post_content, 15)); ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo get_edit_post_link($event->ID); ?>"><?php _e('Edit', 'esp-admin-manager'); ?></a>
                            </span>
                            <?php if (!$is_district_user || current_user_can('manage_options') || current_user_can('administrator')): ?>
                                | <span class="trash">
                                    <a href="<?php echo get_delete_post_link($event->ID); ?>"
                                       onclick="return confirm('<?php _e('Are you sure you want to delete this event?', 'esp-admin-manager'); ?>')"
                                       class="submitdelete"><?php _e('Move to Trash', 'esp-admin-manager'); ?></a>
                                </span>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="toggle-row"><span class="screen-reader-text"><?php _e('Show more details', 'esp-admin-manager'); ?></span></button>
                    </td>
                    <td class="column-dates" data-colname="Date(s)">
                        <?php if ($start_date): ?>
                            <div style="font-weight: bold;">
                                <?php echo esc_html(date('M j, Y', strtotime($start_date))); ?>
                            </div>
                            <?php if ($end_date && $end_date !== $start_date): ?>
                                <div style="font-size: 12px; color: #666;">
                                    to <?php echo esc_html(date('M j, Y', strtotime($end_date))); ?>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #999; font-style: italic;"><?php _e('No date set', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-location" data-colname="Location">
                        <?php if ($location): ?>
                            <?php echo esc_html($location); ?>
                        <?php else: ?>
                            <span style="color: #999; font-style: italic;"><?php _e('No location set', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-district" data-colname="District">
                        <?php if ($district_name): ?>
                            <strong><?php echo esc_html($district_name); ?></strong>
                        <?php else: ?>
                            <span style="color: #999; font-style: italic;"><?php _e('All Districts', 'esp-admin-manager'); ?></span>
                        <?php endif; ?>
                    </td>
                    <td class="column-status" data-colname="Status">
                        <?php if ($event->post_status === 'publish'): ?>
                            <?php if ($event_status === 'upcoming'): ?>
                                <span style="color: #0073aa;">●</span> <?php _e('Upcoming', 'esp-admin-manager'); ?>
                            <?php elseif ($event_status === 'current'): ?>
                                <span style="color: #00a32a;">●</span> <?php _e('Current', 'esp-admin-manager'); ?>
                            <?php else: ?>
                                <span style="color: #666;">●</span> <?php _e('Past', 'esp-admin-manager'); ?>
                            <?php endif; ?>
                        <?php else: ?>
                            <span style="color: #d63638;">●</span> <?php echo esc_html(ucfirst($event->post_status)); ?>
                        <?php endif; ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="notice notice-warning">
            <?php if ($is_district_user && !current_user_can('manage_options') && !current_user_can('administrator')): ?>
                <p><?php _e('No events found for your assigned districts. Contact an administrator to add events for your districts.', 'esp-admin-manager'); ?></p>
            <?php else: ?>
                <p><?php _e('No events found. Click "Add New" to get started.', 'esp-admin-manager'); ?></p>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- Statistics -->
<div class="esp-form-section">
    <h3><?php _e('Event Statistics', 'esp-admin-manager'); ?></h3>
    <div class="esp-stats-grid">
        <div class="esp-stats-item">
            <label><?php _e('Total Events', 'esp-admin-manager'); ?></label>
            <span class="esp-stats-value"><?php echo count($events); ?></span>
        </div>
        <div class="esp-stats-item">
            <label><?php _e('Upcoming Events', 'esp-admin-manager'); ?></label>
            <span class="esp-stats-value">
                <?php 
                $upcoming_count = 0;
                $today = date('Y-m-d');
                foreach ($events as $event) {
                    $start_date = get_post_meta($event->ID, '_esp_event_start_date', true);
                    if ($start_date >= $today) {
                        $upcoming_count++;
                    }
                }
                echo $upcoming_count;
                ?>
            </span>
        </div>
        <div class="esp-stats-item">
            <label><?php _e('Published Events', 'esp-admin-manager'); ?></label>
            <span class="esp-stats-value">
                <?php 
                $published_count = 0;
                foreach ($events as $event) {
                    if ($event->post_status === 'publish') {
                        $published_count++;
                    }
                }
                echo $published_count;
                ?>
            </span>
        </div>
    </div>
</div>
