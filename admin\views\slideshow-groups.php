<?php
/**
 * Slideshow Groups Admin View
 * 
 * @package Provincial_Administration_Manager
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Slideshow Groups', 'esp-admin-manager'); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=provincial-admin-slideshows-add'); ?>" class="page-title-action">
        <?php _e('Add New', 'esp-admin-manager'); ?>
    </a>
    <hr class="wp-header-end">

    <?php settings_errors('slideshow_messages'); ?>

    <div id="esp-slideshow-message"></div>

    <?php if (!empty($groups)): ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th scope="col" class="manage-column column-name column-primary">
                        <?php _e('Name', 'esp-admin-manager'); ?>
                    </th>
                    <th scope="col" class="manage-column column-description">
                        <?php _e('Description', 'esp-admin-manager'); ?>
                    </th>
                    <th scope="col" class="manage-column column-tags">
                        <?php _e('Tags', 'esp-admin-manager'); ?>
                    </th>
                    <th scope="col" class="manage-column column-slides">
                        <?php _e('Slides', 'esp-admin-manager'); ?>
                    </th>
                    <th scope="col" class="manage-column column-shortcode">
                        <?php _e('Shortcode', 'esp-admin-manager'); ?>
                    </th>
                    <th scope="col" class="manage-column column-actions">
                        <?php _e('Actions', 'esp-admin-manager'); ?>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($groups as $group): ?>
                    <?php $slide_count = Provincial_Slideshow::get_slide_count($group->id); ?>
                    <tr>
                        <td class="column-name column-primary" data-colname="<?php _e('Name', 'esp-admin-manager'); ?>">
                            <strong><?php echo esc_html($group->name); ?></strong>
                            <div class="row-actions">
                                <span class="edit">
                                    <a href="#" class="edit-group" data-id="<?php echo $group->id; ?>">
                                        <?php _e('Edit', 'esp-admin-manager'); ?>
                                    </a> |
                                </span>
                                <span class="manage">
                                    <a href="#" class="manage-slides" data-id="<?php echo $group->id; ?>">
                                        <?php _e('Manage Slides', 'esp-admin-manager'); ?>
                                    </a> |
                                </span>
                                <span class="delete">
                                    <a href="#" class="delete-group" data-id="<?php echo $group->id; ?>" style="color: #a00;">
                                        <?php _e('Delete', 'esp-admin-manager'); ?>
                                    </a>
                                </span>
                            </div>
                            <button type="button" class="toggle-row"><span class="screen-reader-text"><?php _e('Show more details', 'esp-admin-manager'); ?></span></button>
                        </td>
                        <td class="column-description" data-colname="<?php _e('Description', 'esp-admin-manager'); ?>">
                            <?php echo esc_html(wp_trim_words($group->description, 10)); ?>
                        </td>
                        <td class="column-tags" data-colname="<?php _e('Tags', 'esp-admin-manager'); ?>">
                            <?php if (!empty($group->tags)): ?>
                                <?php 
                                $tags = explode(',', $group->tags);
                                foreach ($tags as $tag): 
                                    $tag = trim($tag);
                                    if (!empty($tag)):
                                ?>
                                    <span class="esp-tag"><?php echo esc_html($tag); ?></span>
                                <?php 
                                    endif;
                                endforeach; 
                                ?>
                            <?php else: ?>
                                <span class="description"><?php _e('No tags', 'esp-admin-manager'); ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="column-slides" data-colname="<?php _e('Slides', 'esp-admin-manager'); ?>">
                            <span class="slide-count"><?php echo $slide_count; ?></span>
                            <?php if ($slide_count == 0): ?>
                                <span class="description"><?php _e('(No slides)', 'esp-admin-manager'); ?></span>
                            <?php endif; ?>
                        </td>
                        <td class="column-shortcode" data-colname="<?php _e('Shortcode', 'esp-admin-manager'); ?>">
                            <code class="shortcode-display">[esp_slideshow id="<?php echo $group->id; ?>"]</code>
                            <button type="button" class="button button-small copy-shortcode" data-shortcode='[esp_slideshow id="<?php echo $group->id; ?>"]'>
                                <?php _e('Copy', 'esp-admin-manager'); ?>
                            </button>
                            <?php if (!empty($group->tags)): ?>
                                <?php $first_tag = trim(explode(',', $group->tags)[0]); ?>
                                <br><small><?php _e('By tag:', 'esp-admin-manager'); ?> 
                                <code>[esp_slideshow tag="<?php echo esc_attr($first_tag); ?>"]</code></small>
                            <?php endif; ?>
                        </td>
                        <td class="column-actions" data-colname="<?php _e('Actions', 'esp-admin-manager'); ?>">
                            <button type="button" class="button button-small edit-group" data-id="<?php echo $group->id; ?>">
                                <?php _e('Edit', 'esp-admin-manager'); ?>
                            </button>
                            <button type="button" class="button button-small manage-slides" data-id="<?php echo $group->id; ?>">
                                <?php _e('Manage Slides', 'esp-admin-manager'); ?>
                            </button>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="notice notice-info">
            <p>
                <?php _e('No slideshow groups found.', 'esp-admin-manager'); ?>
                <a href="<?php echo admin_url('admin.php?page=provincial-admin-slideshows-add'); ?>">
                    <?php _e('Create your first slideshow group', 'esp-admin-manager'); ?>
                </a>.
            </p>
        </div>
    <?php endif; ?>
</div>

<!-- Edit Group Modal -->
<div id="edit-group-modal" class="esp-modal" style="display: none;">
    <div class="esp-modal-content">
        <div class="esp-modal-header">
            <h3><?php _e('Edit Slideshow Group', 'esp-admin-manager'); ?></h3>
            <button type="button" class="esp-modal-close">&times;</button>
        </div>
        <div class="esp-modal-body">
            <form id="edit-group-form">
                <input type="hidden" id="edit-group-id" name="group_id">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="edit-group-name"><?php _e('Group Name', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="edit-group-name" name="name" required class="regular-text">
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="edit-group-description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <textarea id="edit-group-description" name="description" class="large-text" rows="3"></textarea>
                            <p class="description"><?php _e('Optional description for this slideshow group.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="edit-group-tags"><?php _e('Tags', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="edit-group-tags" name="tags" class="regular-text" placeholder="home, about, contact">
                            <p class="description"><?php _e('Comma-separated tags to identify where this slideshow should appear.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="esp-modal-footer">
            <button type="button" class="button button-primary" id="save-group-changes">
                <?php _e('Update Group', 'esp-admin-manager'); ?>
            </button>
            <button type="button" class="button" id="cancel-edit">
                <?php _e('Cancel', 'esp-admin-manager'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Manage Slides Modal -->
<div id="manage-slides-modal" class="esp-modal esp-modal-large" style="display: none;">
    <div class="esp-modal-content">
        <div class="esp-modal-header">
            <h3><?php _e('Manage Slides', 'esp-admin-manager'); ?></h3>
            <button type="button" class="esp-modal-close">&times;</button>
        </div>
        <div class="esp-modal-body">
            <div id="slides-container">
                <!-- Slides will be loaded here via AJAX -->
            </div>
            
            <hr>
            
            <h4><?php _e('Add New Slide', 'esp-admin-manager'); ?></h4>
            <form id="add-slide-form">
                <input type="hidden" id="slide-group-id" name="group_id">
                <table class="form-table">
                    <tr>
                        <th scope="row">
                            <label for="slide-title"><?php _e('Title', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="text" id="slide-title" name="title" class="regular-text">
                            <p class="description"><?php _e('Optional title for this slide.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="slide-description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <textarea id="slide-description" name="description" class="large-text" rows="3"></textarea>
                            <p class="description"><?php _e('Optional description for this slide.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="slide-image"><?php _e('Image', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="url" id="slide-image" name="image_url" class="regular-text" required>
                            <button type="button" class="button" id="select-image">
                                <?php _e('Select Image', 'esp-admin-manager'); ?>
                            </button>
                            <p class="description"><?php _e('Select an image for this slide.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="slide-link"><?php _e('Link URL', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="url" id="slide-link" name="link_url" class="regular-text">
                            <p class="description"><?php _e('Optional link when slide is clicked.', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                    <tr>
                        <th scope="row">
                            <label for="slide-order"><?php _e('Order', 'esp-admin-manager'); ?></label>
                        </th>
                        <td>
                            <input type="number" id="slide-order" name="order" class="small-text" value="0" min="0">
                            <p class="description"><?php _e('Display order (0 = auto-assign to end).', 'esp-admin-manager'); ?></p>
                        </td>
                    </tr>
                </table>
            </form>
        </div>
        <div class="esp-modal-footer">
            <button type="button" class="button button-primary" id="add-slide-btn">
                <?php _e('Add Slide', 'esp-admin-manager'); ?>
            </button>
            <button type="button" class="button" id="cancel-slides">
                <?php _e('Close', 'esp-admin-manager'); ?>
            </button>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-confirm-modal" class="esp-modal" style="display: none;">
    <div class="esp-modal-content esp-modal-small">
        <div class="esp-modal-header">
            <h3><?php _e('Confirm Delete', 'esp-admin-manager'); ?></h3>
            <button type="button" class="esp-modal-close">&times;</button>
        </div>
        <div class="esp-modal-body">
            <p><?php _e('Are you sure you want to delete this slideshow group? This action cannot be undone and will also delete all slides in this group.', 'esp-admin-manager'); ?></p>
        </div>
        <div class="esp-modal-footer">
            <form method="post" id="delete-group-form">
                <input type="hidden" name="action" value="delete_group">
                <input type="hidden" name="group_id" id="delete-group-id">
                <?php wp_nonce_field('esp_slideshow_action'); ?>
                <button type="submit" class="button button-primary button-delete">
                    <?php _e('Delete', 'esp-admin-manager'); ?>
                </button>
                <button type="button" class="button" id="cancel-delete">
                    <?php _e('Cancel', 'esp-admin-manager'); ?>
                </button>
            </form>
        </div>
    </div>
</div>

<style>
.esp-tag {
    display: inline-block;
    background: #f1f1f1;
    padding: 2px 8px;
    margin: 2px;
    border-radius: 3px;
    font-size: 11px;
}

.shortcode-display {
    background: #f1f1f1;
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-size: 12px;
}

.copy-shortcode {
    margin-left: 5px;
}

.slide-count {
    font-weight: bold;
}
</style>

<script>
jQuery(document).ready(function($) {
    // Copy shortcode functionality
    $('.copy-shortcode').on('click', function() {
        var shortcode = $(this).data('shortcode');
        navigator.clipboard.writeText(shortcode).then(function() {
            // Show success message
            var button = $(this);
            var originalText = button.text();
            button.text('<?php _e('Copied!', 'esp-admin-manager'); ?>');
            setTimeout(function() {
                button.text(originalText);
            }, 2000);
        }.bind(this));
    });
});
</script>
