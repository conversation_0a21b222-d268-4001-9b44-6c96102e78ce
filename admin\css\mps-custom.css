/* Provincial Administration Manager - Custom MPs CRUD Styles */

:root {
    --esp-primary: #006A4E;
    --esp-secondary: #CE1126;
    --esp-accent: #FFD700;
    --esp-light: #f8fafc;
    --esp-dark: #004d3a;
    --esp-success: #10b981;
    --esp-warning: #f59e0b;
    --esp-danger: #ef4444;
    --esp-gray-50: #f9fafb;
    --esp-gray-100: #f3f4f6;
    --esp-gray-200: #e5e7eb;
    --esp-gray-300: #d1d5db;
    --esp-gray-400: #9ca3af;
    --esp-gray-500: #6b7280;
    --esp-gray-600: #4b5563;
    --esp-gray-700: #374151;
    --esp-gray-800: #1f2937;
    --esp-gray-900: #111827;
    --esp-border-radius: 8px;
    --esp-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --esp-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --esp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --esp-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom Admin Wrapper */
.esp-custom-admin-wrap {
    background: var(--esp-gray-50);
    min-height: 100vh;
    margin: 0 -20px 0 -20px;
    padding: 0;
}

/* Header Section */
.esp-admin-header {
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--esp-shadow-md);
}

.esp-header-content {
    flex: 1;
}

.esp-page-title {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.esp-icon {
    font-size: 2.5rem;
}

.esp-page-description {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.esp-header-actions {
    display: flex;
    gap: 1rem;
}

/* Info Banner */
.esp-info-banner {
    background: var(--esp-accent);
    color: var(--esp-gray-800);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--esp-gray-200);
}

.esp-info-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.esp-info-icon {
    font-size: 1.25rem;
}

.esp-info-text {
    font-weight: 500;
}

/* Content Section */
.esp-content-section {
    padding: 2rem;
}

/* MPs Grid */
.esp-mps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* MP Card */
.esp-mp-card {
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--esp-gray-200);
}

.esp-mp-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--esp-shadow-lg);
}

.esp-mp-photo {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: var(--esp-gray-100);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.esp-mp-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center top;
    display: block;
    transition: transform 0.3s ease;
}

.esp-mp-photo:hover img {
    transform: scale(1.05);
}

.esp-mp-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    background: var(--esp-gray-200);
}

.esp-placeholder-icon {
    font-size: 4rem;
    color: var(--esp-gray-400);
}

.esp-mp-info {
    padding: 1.5rem;
}

.esp-mp-name {
    margin: 0 0 1rem 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--esp-primary);
}

.esp-mp-detail {
    display: flex;
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.esp-detail-label {
    font-weight: 500;
    color: var(--esp-gray-600);
    min-width: 80px;
}

.esp-detail-value {
    color: var(--esp-gray-800);
}

.esp-mp-status {
    margin-top: 1rem;
}

.esp-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
}

.esp-status-publish {
    background: var(--esp-success);
    color: white;
}

.esp-status-draft {
    background: var(--esp-warning);
    color: white;
}

.esp-status-private {
    background: var(--esp-gray-500);
    color: white;
}

.esp-mp-actions {
    padding: 1rem 1.5rem;
    background: var(--esp-gray-50);
    border-top: 1px solid var(--esp-gray-200);
    display: flex;
    gap: 0.5rem;
}

/* Buttons */
.esp-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--esp-border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    line-height: 1;
}

.esp-btn:focus {
    outline: 2px solid var(--esp-primary);
    outline-offset: 2px;
}

.esp-btn-primary {
    background: var(--esp-primary);
    color: white;
}

.esp-btn-primary:hover {
    background: var(--esp-dark);
    color: white;
}

.esp-btn-secondary {
    background: var(--esp-gray-200);
    color: var(--esp-gray-700);
}

.esp-btn-secondary:hover {
    background: var(--esp-gray-300);
    color: var(--esp-gray-800);
}

.esp-btn-danger {
    background: var(--esp-danger);
    color: white;
}

.esp-btn-danger:hover {
    background: #dc2626;
    color: white;
}

.esp-btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.esp-btn-icon {
    font-size: 1rem;
}

/* Empty State */
.esp-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
}

.esp-empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.esp-empty-title {
    margin: 0 0 1rem 0;
    font-size: 1.5rem;
    color: var(--esp-gray-700);
}

.esp-empty-description {
    margin: 0 0 2rem 0;
    color: var(--esp-gray-500);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Modal */
.esp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.esp-modal-content {
    position: relative;
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow-lg);
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
}

.esp-modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--esp-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.esp-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    color: var(--esp-primary);
}

.esp-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--esp-gray-400);
    padding: 0;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.esp-modal-close:hover {
    background: var(--esp-gray-100);
    color: var(--esp-gray-600);
}

/* Form Styles */
.esp-form {
    padding: 1.5rem;
}

.esp-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.esp-form-group {
    margin-bottom: 1rem;
}

.esp-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--esp-gray-700);
}

.esp-label.required::after {
    content: ' *';
    color: var(--esp-danger);
}

.esp-input,
.esp-textarea,
.esp-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--esp-gray-300);
    border-radius: var(--esp-border-radius);
    font-size: 0.9rem;
    transition: border-color 0.2s ease;
}

.esp-input:focus,
.esp-textarea:focus,
.esp-select:focus {
    outline: none;
    border-color: var(--esp-primary);
    box-shadow: 0 0 0 3px rgba(0, 106, 78, 0.1);
}

.esp-textarea {
    resize: vertical;
    min-height: 100px;
}

.esp-help-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--esp-gray-500);
}

/* Media Upload */
.esp-media-upload {
    border: 2px dashed var(--esp-gray-300);
    border-radius: var(--esp-border-radius);
    padding: 1rem;
    text-align: center;
    transition: border-color 0.2s ease;
}

.esp-media-upload:hover {
    border-color: var(--esp-primary);
}

.esp-photo-preview {
    margin-bottom: 1rem;
}

.esp-photo-preview img {
    max-width: 200px;
    max-height: 200px;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
}

/* Form Actions */
.esp-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--esp-gray-200);
}

/* Responsive Design */
@media (max-width: 768px) {
    .esp-admin-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .esp-mps-grid {
        grid-template-columns: 1fr;
    }
    
    .esp-form-row {
        grid-template-columns: 1fr;
    }
    
    .esp-modal-content {
        width: 95%;
        margin: 1rem;
    }
    
    .esp-form-actions {
        flex-direction: column;
    }
    
    .esp-mp-actions {
        flex-direction: column;
    }
}
