# Dakoii Prov - Hero Slideshow Plugin Complete Guide

## Overview
This WordPress plugin allows users to create multiple slideshow groups, tag them with keywords, and display specific slideshows on different pages using shortcodes or template functions.

## Plugin Structure
```
dakoii-prov-slideshow/
├── dakoii-prov-slideshow.php (Main plugin file)
├── includes/
│   ├── class-slideshow.php
│   ├── class-admin.php
│   └── class-frontend.php
├── admin/
│   ├── css/
│   │   └── admin-style.css
│   └── js/
│       └── admin-script.js
├── public/
│   ├── css/
│   │   └── slideshow-style.css
│   └── js/
│       └── slideshow-script.js
└── assets/
    └── images/
```

## Step 1: Main Plugin File

**File: `dakoii-prov-slideshow.php`**
```php
<?php
/**
 * Plugin Name: Dakoii Prov - Hero Slideshow
 * Plugin URI: https://yourwebsite.com
 * Description: Create multiple slideshow groups with keyword tags for different pages
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_SLIDESHOW_VERSION', '1.0.0');
define('DAKOII_SLIDESHOW_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_SLIDESHOW_PLUGIN_URL', plugin_dir_url(__FILE__));

// Main plugin class
class DakoiiProvSlideshow {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }
    
    public function init() {
        // Load includes
        $this->load_includes();
        
        // Initialize classes
        if (is_admin()) {
            new DakoiiSlideshowAdmin();
        }
        new DakoiiSlideshowFrontend();
    }
    
    private function load_includes() {
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-slideshow.php';
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-admin.php';
        require_once DAKOII_SLIDESHOW_PLUGIN_DIR . 'includes/class-frontend.php';
    }
    
    public function activate() {
        $this->create_tables();
        flush_rewrite_rules();
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    private function create_tables() {
        global $wpdb;
        
        $charset_collate = $wpdb->get_charset_collate();
        
        // Slideshow groups table
        $table_slideshow_groups = $wpdb->prefix . 'dakoii_slideshow_groups';
        $sql_groups = "CREATE TABLE $table_slideshow_groups (
            id int(11) NOT NULL AUTO_INCREMENT,
            name varchar(255) NOT NULL,
            description text,
            tags text,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        // Slides table
        $table_slides = $wpdb->prefix . 'dakoii_slides';
        $sql_slides = "CREATE TABLE $table_slides (
            id int(11) NOT NULL AUTO_INCREMENT,
            group_id int(11) NOT NULL,
            title varchar(255),
            description text,
            image_url varchar(500) NOT NULL,
            link_url varchar(500),
            slide_order int(11) DEFAULT 0,
            is_active tinyint(1) DEFAULT 1,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (group_id) REFERENCES $table_slideshow_groups(id) ON DELETE CASCADE
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_groups);
        dbDelta($sql_slides);
    }
}

// Initialize the plugin
new DakoiiProvSlideshow();
```

## Step 2: Slideshow Core Class

**File: `includes/class-slideshow.php`**
```php
<?php
class DakoiiSlideshow {
    
    public static function create_group($name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        
        return $wpdb->insert(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags)
            ),
            array('%s', '%s', '%s')
        );
    }
    
    public static function get_groups() {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_results("SELECT * FROM $table ORDER BY name");
    }
    
    public static function get_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_row($wpdb->prepare("SELECT * FROM $table WHERE id = %d", $id));
    }
    
    public static function update_group($id, $name, $description = '', $tags = '') {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        
        return $wpdb->update(
            $table,
            array(
                'name' => sanitize_text_field($name),
                'description' => sanitize_textarea_field($description),
                'tags' => sanitize_text_field($tags)
            ),
            array('id' => $id),
            array('%s', '%s', '%s'),
            array('%d')
        );
    }
    
    public static function delete_group($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->delete($table, array('id' => $id), array('%d'));
    }
    
    public static function add_slide($group_id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        
        return $wpdb->insert(
            $table,
            array(
                'group_id' => $group_id,
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order)
            ),
            array('%d', '%s', '%s', '%s', '%s', '%d')
        );
    }
    
    public static function get_slides($group_id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE group_id = %d AND is_active = 1 ORDER BY slide_order, id", 
            $group_id
        ));
    }
    
    public static function update_slide($id, $title, $description, $image_url, $link_url = '', $order = 0) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        
        return $wpdb->update(
            $table,
            array(
                'title' => sanitize_text_field($title),
                'description' => sanitize_textarea_field($description),
                'image_url' => esc_url_raw($image_url),
                'link_url' => esc_url_raw($link_url),
                'slide_order' => intval($order)
            ),
            array('id' => $id),
            array('%s', '%s', '%s', '%s', '%d'),
            array('%d')
        );
    }
    
    public static function delete_slide($id) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slides';
        return $wpdb->delete($table, array('id' => $id), array('%d'));
    }
    
    public static function get_groups_by_tag($tag) {
        global $wpdb;
        $table = $wpdb->prefix . 'dakoii_slideshow_groups';
        return $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM $table WHERE tags LIKE %s", 
            '%' . $wpdb->esc_like($tag) . '%'
        ));
    }
}
```

## Step 3: Admin Interface Class

**File: `includes/class-admin.php`**
```php
<?php
class DakoiiSlideshowAdmin {
    
    public function __construct() {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_dakoii_slideshow_action', array($this, 'handle_ajax_requests'));
    }
    
    public function add_admin_menu() {
        add_menu_page(
            'Dakoii Slideshows',
            'Slideshows',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page'),
            'dashicons-images-alt2',
            30
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'All Slideshow Groups',
            'All Groups',
            'manage_options',
            'dakoii-slideshows',
            array($this, 'admin_page')
        );
        
        add_submenu_page(
            'dakoii-slideshows',
            'Add New Group',
            'Add New Group',
            'manage_options',
            'dakoii-slideshows-add',
            array($this, 'add_group_page')
        );
    }
    
    public function enqueue_admin_scripts($hook) {
        if (strpos($hook, 'dakoii-slideshows') !== false) {
            wp_enqueue_media();
            wp_enqueue_script(
                'dakoii-admin-js',
                DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/js/admin-script.js',
                array('jquery'),
                DAKOII_SLIDESHOW_VERSION
            );
            wp_enqueue_style(
                'dakoii-admin-css',
                DAKOII_SLIDESHOW_PLUGIN_URL . 'admin/css/admin-style.css',
                array(),
                DAKOII_SLIDESHOW_VERSION
            );
            
            wp_localize_script('dakoii-admin-js', 'dakoii_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dakoii_slideshow_nonce')
            ));
        }
    }
    
    public function admin_page() {
        $groups = DakoiiSlideshow::get_groups();
        ?>
        <div class="wrap">
            <h1>Slideshow Groups <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>" class="page-title-action">Add New</a></h1>
            
            <div id="dakoii-message"></div>
            
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Description</th>
                        <th>Tags</th>
                        <th>Slides</th>
                        <th>Shortcode</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if ($groups): ?>
                        <?php foreach ($groups as $group): ?>
                            <?php $slides = DakoiiSlideshow::get_slides($group->id); ?>
                            <tr>
                                <td><strong><?php echo esc_html($group->name); ?></strong></td>
                                <td><?php echo esc_html($group->description); ?></td>
                                <td><?php echo esc_html($group->tags); ?></td>
                                <td><?php echo count($slides); ?></td>
                                <td><code>[dakoii_slideshow id="<?php echo $group->id; ?>"]</code></td>
                                <td>
                                    <a href="#" class="edit-group" data-id="<?php echo $group->id; ?>">Edit</a> |
                                    <a href="#" class="manage-slides" data-id="<?php echo $group->id; ?>">Manage Slides</a> |
                                    <a href="#" class="delete-group" data-id="<?php echo $group->id; ?>" style="color: #a00;">Delete</a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6">No slideshow groups found. <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows-add'); ?>">Create your first one</a>.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <!-- Edit Group Modal -->
        <div id="edit-group-modal" style="display: none;">
            <div class="modal-content">
                <h3>Edit Slideshow Group</h3>
                <form id="edit-group-form">
                    <input type="hidden" id="edit-group-id" name="group_id">
                    <table class="form-table">
                        <tr>
                            <th><label for="edit-group-name">Group Name</label></th>
                            <td><input type="text" id="edit-group-name" name="name" required class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="edit-group-description">Description</label></th>
                            <td><textarea id="edit-group-description" name="description" class="large-text" rows="3"></textarea></td>
                        </tr>
                        <tr>
                            <th><label for="edit-group-tags">Tags</label></th>
                            <td><input type="text" id="edit-group-tags" name="tags" class="regular-text" placeholder="home, about, contact"></td>
                        </tr>
                    </table>
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Update Group">
                        <button type="button" class="button" id="cancel-edit">Cancel</button>
                    </p>
                </form>
            </div>
        </div>
        
        <!-- Manage Slides Modal -->
        <div id="manage-slides-modal" style="display: none;">
            <div class="modal-content large">
                <h3>Manage Slides</h3>
                <div id="slides-container"></div>
                <hr>
                <h4>Add New Slide</h4>
                <form id="add-slide-form">
                    <input type="hidden" id="slide-group-id" name="group_id">
                    <table class="form-table">
                        <tr>
                            <th><label for="slide-title">Title</label></th>
                            <td><input type="text" id="slide-title" name="title" class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="slide-description">Description</label></th>
                            <td><textarea id="slide-description" name="description" class="large-text" rows="3"></textarea></td>
                        </tr>
                        <tr>
                            <th><label for="slide-image">Image</label></th>
                            <td>
                                <input type="url" id="slide-image" name="image_url" class="regular-text" required>
                                <button type="button" class="button" id="select-image">Select Image</button>
                            </td>
                        </tr>
                        <tr>
                            <th><label for="slide-link">Link URL</label></th>
                            <td><input type="url" id="slide-link" name="link_url" class="regular-text"></td>
                        </tr>
                        <tr>
                            <th><label for="slide-order">Order</label></th>
                            <td><input type="number" id="slide-order" name="order" class="small-text" value="0"></td>
                        </tr>
                    </table>
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Add Slide">
                        <button type="button" class="button" id="cancel-slides">Close</button>
                    </p>
                </form>
            </div>
        </div>
        <?php
    }
    
    public function add_group_page() {
        ?>
        <div class="wrap">
            <h1>Add New Slideshow Group</h1>
            
            <div id="dakoii-message"></div>
            
            <form id="add-group-form">
                <table class="form-table">
                    <tr>
                        <th><label for="group-name">Group Name</label></th>
                        <td><input type="text" id="group-name" name="name" required class="regular-text"></td>
                    </tr>
                    <tr>
                        <th><label for="group-description">Description</label></th>
                        <td><textarea id="group-description" name="description" class="large-text" rows="5"></textarea></td>
                    </tr>
                    <tr>
                        <th><label for="group-tags">Tags</label></th>
                        <td>
                            <input type="text" id="group-tags" name="tags" class="regular-text" placeholder="home, about, contact">
                            <p class="description">Comma-separated tags to identify where this slideshow should appear.</p>
                        </td>
                    </tr>
                </table>
                
                <p class="submit">
                    <input type="submit" class="button-primary" value="Create Group">
                    <a href="<?php echo admin_url('admin.php?page=dakoii-slideshows'); ?>" class="button">Cancel</a>
                </p>
            </form>
        </div>
        <?php
    }
    
    public function handle_ajax_requests() {
        check_ajax_referer('dakoii_slideshow_nonce', 'nonce');
        
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }
        
        $action = sanitize_text_field($_POST['slideshow_action']);
        
        switch ($action) {
            case 'create_group':
                $result = DakoiiSlideshow::create_group(
                    $_POST['name'],
                    $_POST['description'],
                    $_POST['tags']
                );
                wp_send_json_success('Group created successfully');
                break;
                
            case 'update_group':
                $result = DakoiiSlideshow::update_group(
                    intval($_POST['group_id']),
                    $_POST['name'],
                    $_POST['description'],
                    $_POST['tags']
                );
                wp_send_json_success('Group updated successfully');
                break;
                
            case 'delete_group':
                $result = DakoiiSlideshow::delete_group(intval($_POST['group_id']));
                wp_send_json_success('Group deleted successfully');
                break;
                
            case 'get_group':
                $group = DakoiiSlideshow::get_group(intval($_POST['group_id']));
                wp_send_json_success($group);
                break;
                
            case 'add_slide':
                $result = DakoiiSlideshow::add_slide(
                    intval($_POST['group_id']),
                    $_POST['title'],
                    $_POST['description'],
                    $_POST['image_url'],
                    $_POST['link_url'],
                    intval($_POST['order'])
                );
                wp_send_json_success('Slide added successfully');
                break;
                
            case 'get_slides':
                $slides = DakoiiSlideshow::get_slides(intval($_POST['group_id']));
                wp_send_json_success($slides);
                break;
                
            case 'delete_slide':
                $result = DakoiiSlideshow::delete_slide(intval($_POST['slide_id']));
                wp_send_json_success('Slide deleted successfully');
                break;
                
            default:
                wp_send_json_error('Invalid action');
        }
        
        wp_die();
    }
}
```

## Step 4: Frontend Display Class

**File: `includes/class-frontend.php`**
```php
<?php
class DakoiiSlideshowFrontend {
    
    public function __construct() {
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
        add_shortcode('dakoii_slideshow', array($this, 'shortcode_handler'));
        add_action('init', array($this, 'add_template_function'));
    }
    
    public function enqueue_scripts() {
        wp_enqueue_style(
            'dakoii-slideshow-css',
            DAKOII_SLIDESHOW_PLUGIN_URL . 'public/css/slideshow-style.css',
            array(),
            DAKOII_SLIDESHOW_VERSION
        );
        
        wp_enqueue_script(
            'dakoii-slideshow-js',
            DAKOII_SLIDESHOW_PLUGIN_URL . 'public/js/slideshow-script.js',
            array('jquery'),
            DAKOII_SLIDESHOW_VERSION,
            true
        );
    }
    
    public function shortcode_handler($atts) {
        $atts = shortcode_atts(array(
            'id' => 0,
            'tag' => '',
            'autoplay' => true,
            'duration' => 5000,
            'show_nav' => true,
            'show_dots' => true
        ), $atts);
        
        if ($atts['id']) {
            return $this->render_slideshow_by_id($atts['id'], $atts);
        } elseif ($atts['tag']) {
            return $this->render_slideshow_by_tag($atts['tag'], $atts);
        }
        
        return '<p>No slideshow specified.</p>';
    }
    
    private function render_slideshow_by_id($group_id, $options) {
        $slides = DakoiiSlideshow::get_slides($group_id);
        if (!$slides) {
            return '<p>No slides found for this slideshow.</p>';
        }
        
        return $this->render_slideshow($slides, $group_id, $options);
    }
    
    private function render_slideshow_by_tag($tag, $options) {
        $groups = DakoiiSlideshow::get_groups_by_tag($tag);
        if (!$groups) {
            return '<p>No slideshow found with tag: ' . esc_html($tag) . '</p>';
        }
        
        // Use the first group found with this tag
        $group = $groups[0];
        $slides = DakoiiSlideshow::get_slides($group->id);
        
        if (!$slides) {
            return '<p>No slides found for this slideshow.</p>';
        }
        
        return $this->render_slideshow($slides, $group->id, $options);
    }
    
    private function render_slideshow($slides, $group_id, $options) {
        $slideshow_id = 'dakoii-slideshow-' . $group_id . '-' . rand(1000, 9999);
        
        ob_start();
        ?>
        <div class="dakoii-slideshow" id="<?php echo $slideshow_id; ?>" 
             data-autoplay="<?php echo $options['autoplay'] ? 'true' : 'false'; ?>"
             data-duration="<?php echo intval($options['duration']); ?>">
            
            <div class="slideshow-container">
                <?php foreach ($slides as $index => $slide): ?>
                    <div class="slide <?php echo $index === 0 ? 'active' : ''; ?>">
                        <?php if ($slide->link_url): ?>
                            <a href="<?php echo esc_url($slide->link_url); ?>">
                        <?php endif; ?>
                        
                        <img src="<?php echo esc_url($slide->image_url); ?>" 
                             alt="<?php echo esc_attr($slide->title); ?>">
                        
                        <?php if ($slide->title || $slide->description): ?>
                            <div class="slide-content">
                                <?php if ($slide->title): ?>
                                    <h3><?php echo esc_html($slide->title); ?></h3>
                                <?php endif; ?>
                                <?php if ($slide->description): ?>
                                    <p><?php echo esc_html($slide->description); ?></p>
                                <?php endif; ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if ($slide->link_url): ?>
                            </a>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <?php if ($options['show_nav'] && count($slides) > 1): ?>
                <button class="slideshow-nav prev" onclick="dakoiiSlideshowPrev('<?php echo $slideshow_id; ?>')">‹</button>
                <button class="slideshow-nav next" onclick="dakoiiSlideshowNext('<?php echo $slideshow_id; ?>')">›</button>
            <?php endif; ?>
            
            <?php if ($options['show_dots'] && count($slides) > 1): ?>
                <div class="slideshow-dots">
                    <?php foreach ($slides as $index => $slide): ?>
                        <button class="dot <?php echo $index === 0 ? 'active' : ''; ?>" 
                                onclick="dakoiiSlideshowGoTo('<?php echo $slideshow_id; ?>', <?php echo $index; ?>)"></button>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
        
        return ob_get_clean();
    }
    
    public function add_template_function() {
        // Make function available for theme developers
        if (!function_exists('dakoii_get_slideshow')) {
            function dakoii_get_slideshow($id = 0, $tag = '', $options = array()) {
                $frontend = new DakoiiSlideshowFrontend();
                $atts = array_merge(array(
                    'id' => $id,
                    'tag' => $tag
                ), $options);
                return $frontend->shortcode_handler($atts);
            }
        }
    }
}
```

## Step 5: CSS Styles

**File: `admin/css/admin-style.css`**
```css
.dakoii-slideshow-admin {
    max-width: 1200px;
}

#edit-group-modal,
#manage-slides-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 100000;
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 20px;
    border-radius: 5px;
    max-width: 600px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
}

.modal-content.large {
    max-width: 800px;
}

.slide-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 5px;
    background: #f9f9f9;
}

.slide-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.slide-preview img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: 3px;
}

.slide-info {
    flex: 1;
}

.slide-actions {
    display: flex;
    gap: 10px;
}

.notice {
    margin: 15px 0;
}

.shortcode-display {
    background: #f1f1f1;
    padding: 5px 10px;
    border-radius: 3px;
    font-family: monospace;
}
```

**File: `public/css/slideshow-style.css`**
```css
.dakoii-slideshow {
    position: relative;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.slideshow-container {
    position: relative;
    width: 100%;
    height: 400px; /* Default height, can be customized */
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.5s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-