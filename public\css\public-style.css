/* Provincial Administration Manager - Public Styles */

:root {
    --png-red: #CE1126;
    --png-green: #006A4E;
    --png-yellow: #FFD700;
    --dark-green: #004d3a;
    --light-green: #00a86b;
    --cream: #FFF8DC;
    --dark-brown: #8B4513;
    --official-blue: #1e3a8a;
    --light-gray: #f8fafc;
    --medium-gray: #64748b;
}

/* Governor Section */
.esp-governor-section {
    padding: 2rem 0;
    background: white;
}

.esp-governor-grid {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-governor-photo {
    width: 100%;
    max-width: 300px;
    min-height: 200px;
    background: linear-gradient(45deg, var(--png-green), var(--png-red));
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 3rem;
    box-shadow: 0 10px 25px rgba(0,0,0,0.2);
    position: relative;
    overflow: hidden;
}

.esp-governor-photo img {
    width: 100%;
    height: auto;
    max-height: 400px;
    object-fit: cover;
    border-radius: 15px;
    display: block;
}

.esp-placeholder-photo {
    font-size: 4rem;
    color: white;
}

.esp-governor-content h2 {
    font-size: 2.5rem;
    color: var(--png-green);
    margin-bottom: 0.5rem;
}

.esp-governor-title {
    font-size: 1.2rem;
    color: var(--png-red);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.esp-governor-party {
    font-size: 1rem;
    color: var(--medium-gray);
    margin-bottom: 1.5rem;
}

.esp-governor-message {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #555;
    font-style: italic;
    border-left: 4px solid var(--png-yellow);
    padding-left: 1.5rem;
}

/* Parliament Members Section */
.esp-parliament-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-mp-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-mp-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--png-red);
}

.esp-mp-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.esp-mp-photo {
    width: 80px;
    height: 80px;
    background: var(--png-green);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
    margin: 0 auto 1rem;
    overflow: hidden;
}

.esp-mp-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
    display: block;
}

.esp-mp-name {
    font-size: 1.2rem;
    color: var(--png-green);
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.esp-mp-electorate {
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-mp-party {
    color: var(--medium-gray);
    font-size: 0.9rem;
}

.esp-mp-district {
    color: var(--png-green);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.esp-mp-message {
    font-size: 0.9rem;
    line-height: 1.6;
    color: #555;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--cream);
    border-radius: 8px;
    border-left: 3px solid var(--png-yellow);
    font-style: italic;
}

/* Single MP Display */
.esp-single-mp {
    max-width: 800px;
    margin: 0 auto;
}

.esp-single-mp-card {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    align-items: start;
    text-align: left;
    padding: 2rem;
}

.esp-single-mp-card .esp-mp-photo {
    width: 120px;
    height: 120px;
    font-size: 3rem;
}

.esp-single-mp-card .esp-mp-details {
    flex: 1;
}

.esp-single-mp-card .esp-mp-name {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.esp-single-mp-card .esp-mp-message {
    margin-top: 1.5rem;
    font-size: 1rem;
}

/* Districts Section */
.esp-districts-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-districts-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Ensure 2 columns on tablets and larger screens */
@media (min-width: 481px) {
    .esp-districts-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Statistics responsive design */
@media (max-width: 768px) {
    .esp-map-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .esp-map-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 1rem;
    }
}

.esp-district-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid var(--png-green);
}

.esp-district-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
}

.esp-district-photo {
    width: 100%;
    height: 200px;
    overflow: hidden;
    background: var(--png-green);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.esp-district-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.esp-district-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--png-green), var(--png-red));
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-placeholder-icon {
    font-size: 4rem;
    color: white;
    opacity: 0.8;
}

.esp-district-info {
    padding: 1.5rem;
    text-align: center;
}

.esp-district-name {
    font-size: 1.3rem;
    color: var(--png-green);
    font-weight: bold;
    margin: 0 0 1rem 0;
}

.esp-district-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.esp-district-stat {
    text-align: center;
    padding: 0.75rem;
    background: var(--cream);
    border-radius: 8px;
    border: 1px solid rgba(0, 106, 78, 0.1);
}

.esp-stat-number {
    display: block;
    font-size: 1.4rem;
    font-weight: bold;
    color: var(--png-red);
    margin-bottom: 0.25rem;
}

.esp-stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--medium-gray);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-district-description {
    color: #666;
    line-height: 1.6;
    font-size: 0.9rem;
    margin-top: 1rem;
    padding: 1rem;
    background: var(--cream);
    border-radius: 8px;
    border-left: 3px solid var(--png-yellow);
    font-style: italic;
}

/* Statistics Section */
.esp-statistics-section {
    padding: 2rem 0;
}

.esp-map-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-map-stats.list {
    grid-template-columns: 1fr;
    max-width: 600px;
}

.esp-map-stat {
    background: var(--light-gray);
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    border-left: 4px solid var(--png-yellow);
}

.esp-map-stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: var(--png-red);
}

.esp-map-stat-label {
    color: var(--medium-gray);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Events Section */
.esp-events-section {
    padding: 2rem 0;
    background: var(--light-gray);
}

.esp-events-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-event-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--png-red);
    position: relative;
}

.esp-event-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    border-left-color: var(--png-green);
}

.esp-event-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.esp-event-link:hover {
    text-decoration: none;
    color: inherit;
}

.esp-event-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
    background: var(--light-gray);
    border-bottom: 3px solid var(--png-yellow);
}

.esp-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.esp-event-card:hover .esp-event-image img {
    transform: scale(1.05);
}

.esp-event-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--png-green);
    background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.esp-event-content {
    padding: 1.5rem;
}

.esp-event-date {
    font-size: 0.9rem;
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-event-title {
    font-size: 1.3rem;
    color: var(--png-green);
    margin-bottom: 0.8rem;
    font-weight: bold;
    line-height: 1.3;
}

.esp-event-location {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.esp-event-description {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.esp-event-read-more {
    color: var(--png-red);
    font-size: 0.9rem;
    font-weight: 600;
    text-align: right;
    margin-top: auto;
    padding-top: 0.5rem;
    border-top: 1px solid #f0f0f0;
    transition: color 0.3s ease;
}

.esp-event-card:hover .esp-event-read-more {
    color: var(--png-green);
}

/* Legacy events list style (for backward compatibility) */
.esp-featured-events {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-event-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.esp-event-item:last-child {
    border-bottom: none;
}

/* News Section */
.esp-news-section {
    padding: 2rem 0;
    background: white;
}

.esp-news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-news-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-left: 5px solid var(--png-green);
}

.esp-news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.2);
    border-left-color: var(--png-red);
}

.esp-news-link {
    display: block;
    text-decoration: none;
    color: inherit;
    height: 100%;
}

.esp-news-link:hover {
    text-decoration: none;
    color: inherit;
}

.esp-news-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
    position: relative;
    background: var(--light-gray);
    border-bottom: 3px solid var(--png-yellow);
}

.esp-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.esp-news-card:hover .esp-news-image img {
    transform: scale(1.05);
}

.esp-news-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 4rem;
    color: var(--png-green);
    background: linear-gradient(135deg, var(--cream) 0%, var(--light-gray) 100%);
}

.esp-news-content {
    padding: 1.5rem;
}

.esp-news-date {
    font-size: 0.9rem;
    color: var(--png-red);
    font-weight: 600;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-news-title {
    font-size: 1.3rem;
    color: var(--png-green);
    margin-bottom: 0.8rem;
    font-weight: bold;
    line-height: 1.3;
}

.esp-news-source {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.esp-news-excerpt {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.esp-news-read-more {
    color: var(--png-green);
    font-size: 0.9rem;
    font-weight: 600;
    text-align: right;
    margin-top: auto;
    padding-top: 0.5rem;
    border-top: 1px solid #f0f0f0;
    transition: color 0.3s ease;
}

.esp-news-card:hover .esp-news-read-more {
    color: var(--png-red);
}

/* Legacy news list style (for backward compatibility) */
.esp-featured-news {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-news-item {
    padding: 1rem 0;
    border-bottom: 1px solid #eee;
}

.esp-news-item:last-child {
    border-bottom: none;
}

.esp-news-source {
    font-size: 0.9rem;
    color: var(--medium-gray);
    margin-bottom: 0.5rem;
    font-style: italic;
}

.esp-news-excerpt {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Contact Section */
.esp-contact-section {
    padding: 2rem 0;
    background: var(--png-green);
    color: white;
}

.esp-contact-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.esp-contact-grid.list {
    grid-template-columns: 1fr;
    max-width: 600px;
}

.esp-contact-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.esp-contact-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--png-yellow);
}

.esp-contact-title {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--png-yellow);
}

.esp-contact-info {
    line-height: 1.6;
    opacity: 0.9;
}

.esp-contact-info a {
    color: white;
    text-decoration: none;
}

.esp-contact-info a:hover {
    color: var(--png-yellow);
}

/* Enhanced Slideshow Section */
.esp-slideshow,
.esp-slideshow-section {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    margin: 20px 0;
}

.esp-slideshow-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.esp-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-slide.active {
    opacity: 1;
}

.esp-slide-link {
    display: block;
    width: 100%;
    height: 100%;
    position: relative;
    text-decoration: none;
    color: inherit;
}

.esp-slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.esp-slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
    color: white;
    padding: 40px 30px 30px;
    text-align: left;
}

.esp-slide-content-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 2rem;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 10px;
    padding: 2rem;
}

.esp-slide-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0 0 1rem 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
    line-height: 1.2;
}

.esp-slide-description,
.esp-slide-subtitle {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.95;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
    line-height: 1.4;
}

/* Navigation arrows */
.esp-slideshow-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.6);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    z-index: 10;
}

.esp-slideshow-nav:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: translateY(-50%) scale(1.1);
}

.esp-slideshow-nav.esp-prev {
    left: 20px;
}

.esp-slideshow-nav.esp-next {
    right: 20px;
}

.esp-nav-icon {
    font-weight: bold;
    font-size: 24px;
}

/* Dot indicators */
.esp-slideshow-dots {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 10px;
    z-index: 10;
}

.esp-dot,
.esp-slide-dot {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    border: 2px solid rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
}

.esp-dot.active,
.esp-slide-dot.active {
    background: var(--png-yellow);
    border-color: var(--png-yellow);
    transform: scale(1.2);
}

.esp-dot:hover,
.esp-slide-dot:hover {
    background: rgba(255, 255, 255, 0.9);
    transform: scale(1.1);
}

/* Default slideshow styles (backward compatibility) */
.esp-slideshow-default .esp-slide {
    background-size: cover;
    background-position: center;
}

.esp-slideshow-default .esp-slide:nth-child(1) {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                linear-gradient(45deg, var(--png-green), var(--dark-green));
}

.esp-slideshow-default .esp-slide:nth-child(2) {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                linear-gradient(135deg, var(--png-red), var(--png-yellow));
}

.esp-slideshow-default .esp-slide:nth-child(3) {
    background: linear-gradient(rgba(0,0,0,0.4), rgba(0,0,0,0.4)),
                linear-gradient(90deg, var(--png-yellow), var(--png-green));
}

/* Error message styling */
.esp-slideshow-error {
    background: #f8d7da;
    color: #721c24;
    padding: 15px;
    border: 1px solid #f5c6cb;
    border-radius: 4px;
    margin: 20px 0;
    text-align: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .esp-governor-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .esp-mp-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .esp-slideshow,
    .esp-slideshow-section {
        height: 300px;
        margin: 15px 0;
    }

    .esp-slide-title {
        font-size: 1.8rem;
    }

    .esp-slide-description,
    .esp-slide-subtitle {
        font-size: 1rem;
    }

    .esp-slide-content {
        padding: 30px 20px 20px;
    }

    .esp-slide-content-center {
        padding: 1.5rem;
    }

    .esp-slideshow-nav {
        width: 40px;
        height: 40px;
        font-size: 16px;
    }

    .esp-slideshow-nav.esp-prev {
        left: 10px;
    }

    .esp-slideshow-nav.esp-next {
        right: 10px;
    }

    .esp-dot,
    .esp-slide-dot {
        width: 12px;
        height: 12px;
    }

    .esp-slideshow-dots {
        bottom: 15px;
        gap: 8px;
    }
}

    .esp-map-stats {
        grid-template-columns: 1fr;
    }

    .esp-districts-grid {
        grid-template-columns: 1fr;
    }

    .esp-district-stats {
        grid-template-columns: 1fr;
    }

    .esp-contact-grid {
        grid-template-columns: 1fr;
    }

    .esp-events-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .esp-event-image {
        height: 180px;
    }

    .esp-event-content {
        padding: 1rem;
    }

    .esp-news-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .esp-news-image {
        height: 180px;
    }

    .esp-news-content {
        padding: 1rem;
    }

    .esp-single-event-image,
    .esp-single-news-image {
        height: 200px;
    }

    .esp-single-event-meta,
    .esp-single-news-meta,
    .esp-single-event-content,
    .esp-single-news-content {
        padding: 1.5rem;
    }

    .esp-single-event-date,
    .esp-single-event-location,
    .esp-single-event-contact,
    .esp-single-news-date,
    .esp-single-news-source,
    .esp-single-news-featured {
        font-size: 1rem;
    }

    /* Single MP responsive */
    .esp-single-mp-card {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
        padding: 1.5rem;
    }

    .esp-single-mp-card .esp-mp-photo {
        width: 100px;
        height: 100px;
        margin: 0 auto 1rem;
    }

    .esp-single-mp-card .esp-mp-name {
        font-size: 1.3rem;
    }

    .esp-single-mp-card .esp-mp-message {
        margin-top: 1rem;
        padding: 0.8rem;
    }
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.esp-mp-card,
.esp-district-card,
.esp-event-card,
.esp-event-item,
.esp-news-card,
.esp-news-item {
    animation: fadeInUp 0.6s ease-out;
}

/* Single Event Page Styles */
.esp-single-event-header {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 5px solid var(--png-red);
}

.esp-single-event-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-bottom: 3px solid var(--png-yellow);
}

.esp-single-event-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.esp-single-event-meta {
    padding: 2rem;
    background: var(--light-gray);
}

.esp-single-event-date,
.esp-single-event-location,
.esp-single-event-contact {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.esp-single-event-date:last-child,
.esp-single-event-location:last-child,
.esp-single-event-contact:last-child {
    margin-bottom: 0;
}

.esp-meta-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
    width: 1.5rem;
    text-align: center;
}

.esp-single-event-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    line-height: 1.8;
    font-size: 1.1rem;
}

.esp-single-event-footer {
    text-align: center;
    margin-bottom: 2rem;
}

.esp-back-button {
    display: inline-block;
    background: var(--png-green);
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 3px 10px rgba(0,0,0,0.2);
}

.esp-back-button:hover {
    background: var(--png-red);
    color: white;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

/* Single News Page Styles */
.esp-single-news-header {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    border-left: 5px solid var(--png-green);
}

.esp-single-news-image {
    width: 100%;
    height: 300px;
    overflow: hidden;
    border-bottom: 3px solid var(--png-yellow);
}

.esp-single-news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

.esp-single-news-meta {
    padding: 2rem;
    background: var(--light-gray);
}

.esp-single-news-date,
.esp-single-news-source,
.esp-single-news-featured {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.esp-single-news-date:last-child,
.esp-single-news-source:last-child,
.esp-single-news-featured:last-child {
    margin-bottom: 0;
}

.esp-single-news-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    line-height: 1.8;
    font-size: 1.1rem;
}

.esp-single-news-footer {
    text-align: center;
    margin-bottom: 2rem;
}



/* Maps Section */
.esp-map-container {
    margin: 2rem 0;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.esp-map-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--png-green), var(--dark-green));
    color: white;
}

.esp-map-header h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.esp-map-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.95rem;
}

.esp-map-display {
    min-height: 400px;
    position: relative;
    background: #f8f9fa;
}

.esp-map-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    color: var(--medium-gray);
}

.esp-loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid var(--png-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-top: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.esp-map-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    min-height: 400px;
    text-align: center;
    color: #d63638;
    background: #fff8f8;
    border: 2px dashed #d63638;
    border-radius: 8px;
    padding: 20px;
}

.esp-map-error h4 {
    margin: 0 0 10px 0;
    color: #d63638;
}

.esp-map-error p {
    margin: 5px 0;
}

.esp-map-popup h4 {
    margin: 0 0 10px 0;
    color: var(--png-green);
    font-size: 16px;
}

.esp-map-popup p {
    margin: 5px 0;
    font-size: 14px;
}

.esp-map-title-control h5 {
    margin: 0;
    color: var(--png-green);
    font-size: 14px;
    font-weight: 600;
}

.esp-map-placeholder {
    text-align: center;
    color: var(--medium-gray);
}

.esp-map-placeholder p {
    margin: 0.5rem 0;
}

.esp-maps-list {
    display: grid;
    gap: 2rem;
}

.esp-map-item {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.esp-map-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.15);
}

.esp-map-item h4 {
    margin: 0 0 1rem 0;
    color: var(--png-green);
    font-size: 1.3rem;
    font-weight: 600;
}

.esp-map-item p {
    margin: 0 0 1.5rem 0;
    color: var(--medium-gray);
    line-height: 1.6;
}

/* Responsive Maps */
@media (max-width: 768px) {
    .esp-map-container {
        margin: 1rem 0;
    }

    .esp-map-header {
        padding: 1rem;
    }

    .esp-map-header h3 {
        font-size: 1.2rem;
    }

    .esp-map-display {
        min-height: 300px;
    }

    .esp-maps-list {
        gap: 1rem;
    }

    .esp-map-loading,
    .esp-map-error {
        min-height: 300px;
        padding: 15px;
    }

    .esp-map-title-control {
        font-size: 12px !important;
    }

    .esp-map-popup h4 {
        font-size: 14px;
    }

    .esp-map-popup p {
        font-size: 12px;
    }
}

/* Districts responsive design - only for mobile */
@media (max-width: 480px) {
    .esp-districts-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
        padding: 0 1rem;
    }

    .esp-district-photo {
        height: 150px;
    }

    .esp-district-info {
        padding: 1rem;
    }

    .esp-district-name {
        font-size: 1.1rem;
    }

    .esp-district-stats {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .esp-district-description {
        padding: 0.75rem;
        margin-top: 0.75rem;
    }
}

/* Leaflet map controls styling */
.leaflet-control-zoom a {
    background-color: var(--png-green) !important;
    color: white !important;
    border: 1px solid var(--dark-green) !important;
}

/* Simple print button hiding */
.print-button,
.print-btn,
.esp-print-btn,
button[onclick*="print"],
a[onclick*="print"],
input[value*="Print"] {
    display: none !important;
}

/* Hide print buttons in our specific sections */
.esp-governor-section .esp-print-btn,
.esp-parliament-section .esp-print-btn,
.esp-districts-section .esp-print-btn {
    display: none !important;
}

.leaflet-control-zoom a:hover {
    background-color: var(--dark-green) !important;
}

.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.leaflet-popup-tip {
    background: white;
}
