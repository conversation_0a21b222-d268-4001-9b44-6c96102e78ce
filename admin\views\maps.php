<?php
/**
 * Maps Admin View
 * 
 * Admin interface for managing maps and JSON boundary files
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Get existing maps
$maps = get_posts(array(
    'post_type' => 'esp_map',
    'numberposts' => -1,
    'post_status' => 'publish'
));
?>

<div class="wrap">
    <h1><?php _e('Maps Management', 'esp-admin-manager'); ?></h1>
    
    <?php settings_errors('esp_messages'); ?>
    
    <!-- Upload Form -->
    <div class="esp-form-section">
        <h2><?php _e('Upload New Map', 'esp-admin-manager'); ?></h2>
        <form method="post" enctype="multipart/form-data" class="esp-form">
            <?php wp_nonce_field('provincial_maps_nonce', 'provincial_nonce'); ?>
            
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="map_title"><?php _e('Map Title', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="text" id="map_title" name="map_title" class="regular-text" required>
                        <p class="description"><?php _e('Enter a descriptive title for this map.', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="map_description"><?php _e('Description', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <textarea id="map_description" name="map_description" rows="3" class="large-text"></textarea>
                        <p class="description"><?php _e('Optional description for this map.', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="map_json_file"><?php _e('JSON Boundary File', 'esp-admin-manager'); ?></label>
                    </th>
                    <td>
                        <input type="file" id="map_json_file" name="map_json_file" accept=".json" required>
                        <p class="description"><?php _e('Upload a JSON file containing map boundary data.', 'esp-admin-manager'); ?></p>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="upload_json" class="button-primary" value="<?php _e('Upload Map', 'esp-admin-manager'); ?>">
            </p>
        </form>
    </div>
    
    <!-- Existing Maps -->
    <div class="esp-form-section">
        <h2><?php _e('Existing Maps', 'esp-admin-manager'); ?></h2>
        
        <?php if (!empty($maps)): ?>
        <table class="wp-list-table widefat fixed striped">
            <thead>
                <tr>
                    <th><?php _e('Map Title', 'esp-admin-manager'); ?></th>
                    <th><?php _e('JSON File', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Upload Date', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Shortcode', 'esp-admin-manager'); ?></th>
                    <th><?php _e('Actions', 'esp-admin-manager'); ?></th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($maps as $map): 
                    $json_file = get_post_meta($map->ID, '_esp_map_json_file', true);
                ?>
                <tr>
                    <td><strong><?php echo esc_html($map->post_title); ?></strong></td>
                    <td><?php echo esc_html($json_file); ?></td>
                    <td><?php echo get_the_date('Y-m-d H:i', $map->ID); ?></td>
                    <td>
                        <code>[dakoii_map id="<?php echo $map->ID; ?>"]</code>
                        <button type="button" class="button button-small copy-shortcode" data-shortcode='[dakoii_map id="<?php echo $map->ID; ?>"]'>
                            <?php _e('Copy', 'esp-admin-manager'); ?>
                        </button>
                    </td>
                    <td>
                        <a href="<?php echo get_edit_post_link($map->ID); ?>" class="button button-small">
                            <?php _e('Edit', 'esp-admin-manager'); ?>
                        </a>
                        <a href="<?php echo get_delete_post_link($map->ID); ?>" class="button button-small button-link-delete"
                           onclick="return confirm('<?php _e('Are you sure you want to delete this map?', 'esp-admin-manager'); ?>')">
                            <?php _e('Delete', 'esp-admin-manager'); ?>
                        </a>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php else: ?>
        <p><?php _e('No maps uploaded yet.', 'esp-admin-manager'); ?></p>
        <?php endif; ?>
    </div>

    <!-- Usage Instructions -->
    <div class="esp-form-section">
        <h2><?php _e('Usage Instructions', 'esp-admin-manager'); ?></h2>
        <p><?php _e('Use the following shortcodes to display maps on your website:', 'esp-admin-manager'); ?></p>
        <ul>
            <li>
                <strong><code>[dakoii_map id="123"]</code></strong> - 
                <?php _e('Display a specific map (replace 123 with the actual map ID)', 'esp-admin-manager'); ?>
            </li>
            <li>
                <strong><code>[dakoii_maps]</code></strong> - 
                <?php _e('Display all available maps', 'esp-admin-manager'); ?>
            </li>
        </ul>
        <p><?php _e('You can also customize the map display with additional parameters:', 'esp-admin-manager'); ?></p>
        <ul>
            <li>
                <strong><code>[dakoii_map id="123" width="100%" height="500px"]</code></strong> - 
                <?php _e('Set custom width and height', 'esp-admin-manager'); ?>
            </li>
        </ul>
    </div>
</div>

<script>
// Copy shortcode functionality
document.addEventListener('DOMContentLoaded', function() {
    const copyButtons = document.querySelectorAll('.copy-shortcode');
    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            const shortcode = this.getAttribute('data-shortcode');
            navigator.clipboard.writeText(shortcode).then(() => {
                this.textContent = '<?php _e('Copied!', 'esp-admin-manager'); ?>';
                setTimeout(() => {
                    this.textContent = '<?php _e('Copy', 'esp-admin-manager'); ?>';
                }, 2000);
            });
        });
    });
});
</script>

<style>
.esp-form-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    box-shadow: 0 1px 1px rgba(0,0,0,.04);
    margin: 20px 0;
    padding: 20px;
}

.esp-form-section h2 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.copy-shortcode {
    margin-left: 10px;
}

.button-link-delete {
    color: #a00;
}

.button-link-delete:hover {
    color: #dc3232;
}
</style>
