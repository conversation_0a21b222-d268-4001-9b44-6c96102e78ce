/* Provincial Administration Manager - Custom Districts CRUD Styles */

:root {
    --esp-primary: #006A4E;
    --esp-secondary: #CE1126;
    --esp-accent: #FFD700;
    --esp-light: #f8fafc;
    --esp-dark: #004d3a;
    --esp-success: #10b981;
    --esp-warning: #f59e0b;
    --esp-danger: #ef4444;
    --esp-gray-50: #f9fafb;
    --esp-gray-100: #f3f4f6;
    --esp-gray-200: #e5e7eb;
    --esp-gray-300: #d1d5db;
    --esp-gray-400: #9ca3af;
    --esp-gray-500: #6b7280;
    --esp-gray-600: #4b5563;
    --esp-gray-700: #374151;
    --esp-gray-800: #1f2937;
    --esp-gray-900: #111827;
    --esp-border-radius: 8px;
    --esp-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --esp-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --esp-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --esp-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Custom Admin Wrapper */
.esp-custom-admin-wrap {
    background: var(--esp-gray-50);
    min-height: 100vh;
    margin: 0 -20px 0 -20px;
    padding: 0;
}

/* Header Section */
.esp-admin-header {
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    color: white;
    padding: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: var(--esp-shadow-md);
}

.esp-header-content {
    flex: 1;
}

.esp-page-title {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.esp-icon {
    font-size: 2.5rem;
}

.esp-page-description {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

.esp-header-actions {
    display: flex;
    gap: 1rem;
}

/* Info Banner */
.esp-info-banner {
    background: linear-gradient(135deg, var(--esp-accent), #f4d03f);
    color: var(--esp-gray-800);
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--esp-gray-200);
}

.esp-info-content {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.esp-info-icon {
    font-size: 1.5rem;
}

.esp-info-text {
    font-size: 1rem;
}

/* Content Section */
.esp-content-section {
    padding: 2rem;
}

/* Districts Grid */
.esp-districts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.esp-district-card {
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid var(--esp-gray-200);
}

.esp-district-card:hover {
    box-shadow: var(--esp-shadow-lg);
    transform: translateY(-2px);
}

.esp-district-photo {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.esp-district-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.esp-district-placeholder {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-placeholder-icon {
    font-size: 4rem;
    color: white;
    opacity: 0.8;
}

.esp-district-info {
    padding: 1.5rem;
}

.esp-district-name {
    margin: 0 0 0.5rem 0;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--esp-gray-900);
}

.esp-district-description {
    color: var(--esp-gray-600);
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1rem;
}

.esp-district-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.esp-stat-item {
    text-align: center;
    padding: 0.5rem;
    background: var(--esp-gray-50);
    border-radius: 4px;
    border: 1px solid var(--esp-gray-200);
}

.esp-stat-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--esp-primary);
}

.esp-stat-label {
    display: block;
    font-size: 0.75rem;
    color: var(--esp-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-district-status {
    margin-bottom: 1rem;
}

.esp-status {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.esp-status-publish {
    background: var(--esp-success);
    color: white;
}

.esp-status-draft {
    background: var(--esp-warning);
    color: white;
}

.esp-status-private {
    background: var(--esp-gray-500);
    color: white;
}

.esp-district-actions {
    display: flex;
    gap: 0.5rem;
    padding: 0 1.5rem 1.5rem;
}

/* Buttons */
.esp-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--esp-border-radius);
    font-size: 0.9rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    justify-content: center;
}

.esp-btn-primary {
    background: var(--esp-primary);
    color: white;
}

.esp-btn-primary:hover {
    background: var(--esp-dark);
    transform: translateY(-1px);
}

.esp-btn-secondary {
    background: var(--esp-gray-100);
    color: var(--esp-gray-700);
    border: 1px solid var(--esp-gray-300);
}

.esp-btn-secondary:hover {
    background: var(--esp-gray-200);
    border-color: var(--esp-gray-400);
}

.esp-btn-danger {
    background: var(--esp-danger);
    color: white;
}

.esp-btn-danger:hover {
    background: #dc2626;
    transform: translateY(-1px);
}

.esp-btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.esp-btn-icon {
    font-size: 1rem;
}

/* Empty State */
.esp-empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
    margin: 2rem 0;
}

.esp-empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.esp-empty-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--esp-gray-900);
    margin: 0 0 0.5rem 0;
}

.esp-empty-description {
    color: var(--esp-gray-600);
    font-size: 1rem;
    line-height: 1.6;
    margin: 0 0 2rem 0;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Statistics Section */
.esp-stats-section {
    padding: 0 2rem 2rem;
}

.esp-section-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--esp-gray-900);
    margin: 0 0 1.5rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--esp-primary);
}

.esp-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.esp-stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    border-left: 4px solid var(--esp-primary);
}

.esp-stat-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.esp-stat-content {
    flex: 1;
}

.esp-stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--esp-primary);
    line-height: 1;
}

.esp-stat-card .esp-stat-label {
    font-size: 0.9rem;
    color: var(--esp-gray-600);
    margin-top: 0.25rem;
    text-transform: none;
    letter-spacing: normal;
}

/* Modal Styles */
.esp-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 100000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.esp-modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.esp-modal-content {
    position: relative;
    background: white;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow-lg);
    width: 90%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    z-index: 1;
}

.esp-modal-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--esp-gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--esp-primary), var(--esp-dark));
    color: white;
    border-radius: var(--esp-border-radius) var(--esp-border-radius) 0 0;
}

.esp-modal-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.esp-modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.esp-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Form Styles */
.esp-form {
    padding: 2rem;
}

.esp-form-section {
    margin-bottom: 2rem;
}

.esp-form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.esp-form-row:has(.esp-form-group:nth-child(2)) {
    grid-template-columns: 1fr 1fr;
}

.esp-form-group {
    display: flex;
    flex-direction: column;
}

.esp-form-label {
    font-weight: 600;
    color: var(--esp-gray-700);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.esp-required {
    color: var(--esp-danger);
}

.esp-form-input,
.esp-form-textarea {
    padding: 0.75rem;
    border: 1px solid var(--esp-gray-300);
    border-radius: var(--esp-border-radius);
    font-size: 0.9rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.esp-form-input:focus,
.esp-form-textarea:focus {
    outline: none;
    border-color: var(--esp-primary);
    box-shadow: 0 0 0 3px rgba(0, 106, 78, 0.1);
}

.esp-form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Media Upload */
.esp-media-upload {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.esp-photo-preview {
    position: relative;
    display: inline-block;
}

.esp-photo-preview img {
    max-width: 200px;
    max-height: 150px;
    border-radius: var(--esp-border-radius);
    box-shadow: var(--esp-shadow);
}

.esp-photo-preview .esp-btn {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
}

/* Form Actions */
.esp-form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid var(--esp-gray-200);
}

/* Responsive Design */
@media (max-width: 768px) {
    .esp-admin-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .esp-districts-grid {
        grid-template-columns: 1fr;
    }

    .esp-form-row {
        grid-template-columns: 1fr;
    }

    .esp-modal-content {
        width: 95%;
        margin: 1rem;
    }

    .esp-form-actions {
        flex-direction: column;
    }

    .esp-district-actions {
        flex-direction: column;
    }

    .esp-stats-grid {
        grid-template-columns: 1fr;
    }

    .esp-district-stats {
        grid-template-columns: 1fr;
    }
}
